# Daytona.io Migration Plan

## Overview

Replace the complex Fly.io implementation with a simpler Daytona.io implementation for live preview functionality. This migration will remove unnecessary complexity while maintaining the core preview capabilities.

## Phase 1: Remove Fly.io Implementation

### 1.1 Remove Fly.io Files and Code

**Files to Remove:**
- `apps/core/src/preview/fly-preview-manager.ts` - Complete Fly.io manager class
- `apps/core/src/preview/fly-types.ts` - All Fly.io TypeScript types
- `apps/core/src/preview/fly-axios-instance.ts` - Fly.io API client
- `docs/fly-preview-implementation.md` - Fly.io documentation
- `docs/fly-implementation-summary.md` - Fly.io summary

**Code to Remove from Existing Files:**
- `apps/core/src/preview/preview.service.ts`:
  - Remove FlyPreviewManager import and initialization
  - Remove all Fly.io related methods
  - Remove Fly.io environment variable checks
- `apps/core/src/preview/preview.controller.ts`:
  - Remove `/upload` endpoint (Fly.io specific)
  - Remove `/health` endpoint
  - Remove `/cleanup` endpoint
  - Remove `/create-machine` endpoint
  - Remove `/logs/:appName/:machineId` endpoint

### 1.2 Remove Fly.io Database Tables and Columns

**Database Schema Changes:**
- `packages/database/prisma/schema/user.prisma`:
  - Remove `fly_app_name` field from User model
  - Remove `user_machine` relationship
- `packages/database/prisma/schema/userMachine.prisma`:
  - Remove entire UserMachine model (delete file)

**Migration Commands:**
```bash
cd packages/database
pnpx prisma migrate dev --name remove_fly_io_tables
pnpx prisma generate
```

### 1.3 Remove Fly.io Environment Variables

**Environment Variables to Remove:**
- `FLY_API_TOKEN`
- `FLY_ORG_SLUG`
- `DOCKER_IMAGE`
- `PREVIEW_TIMEOUT`

## Phase 2: Implement Daytona.io Integration

### 2.1 Update Base Store for Daytona

**File:** `apps/admin/src/store/base-store.ts`

**Add New State:**
```typescript
interface BaseState {
  // ... existing fields
  sandboxId: string | null; // Store current Daytona sandbox ID
  setSandboxId: (id: string | null) => void;
  previewUrl: string | null; // Store current preview URL
  setPreviewUrl: (url: string | null) => void;
  // Update clearAll to include new fields
}
```

### 2.2 Create Daytona Service

**File:** `apps/core/src/preview/daytona.service.ts`

**Key Methods:**
- `createSandbox()` - Create new Daytona sandbox
- `destroySandbox(sandboxId: string)` - Destroy existing sandbox
- `uploadFiles(sandboxId: string, files: Record<string, string>)` - Upload files to sandbox
- `healthCheck(previewUrl: string)` - Check if sandbox is ready
- `getPreviewUrl(sandboxId: string)` - Get preview URL for sandbox

### 2.3 Update Preview Controller

**File:** `apps/core/src/preview/preview.controller.ts`

**New Endpoints:**
```typescript
@Post('create-sandbox')
@UseGuards(JwtAuthGuard)
async createSandbox(@Req() req: ApiRequest) {
  // Create Daytona sandbox and return sandboxId + previewUrl
}

@Post('upload-files')
@UseGuards(JwtAuthGuard)
async uploadFiles(
  @Body() { files, sandboxId }: { files: Record<string, string>; sandboxId: string },
  @Req() req: ApiRequest,
) {
  // Upload files to existing sandbox
}

@Delete('destroy-sandbox/:sandboxId')
@UseGuards(JwtAuthGuard)
async destroySandbox(@Param('sandboxId') sandboxId: string) {
  // Destroy sandbox
}
```

### 2.4 Update Preview Service

**File:** `apps/core/src/preview/preview.service.ts`

**Simplified Methods:**
- Remove all Fly.io complexity
- Keep only Daytona integration
- Remove database tracking (no persistent machines needed)

## Phase 3: Frontend Integration

### 3.1 Update Chat Submit Handler

**File:** `apps/admin/src/features/chat/components/chat-left.tsx`

**Modified handleSubmit:**
```typescript
const handleSubmit = () => {
  setBuildError('');
  setShowErrorCard(false);
  setView('code');
  setLogs('');
  setError(undefined);
  setShowLogs(false);

  // Start Daytona sandbox creation in background
  createDaytonaSandboxInBackground();

  handleChatSubmit(undefined, {
    experimental_attachments: files,
  });

  setFiles(undefined);
  // ... rest of cleanup
};
```

**New Background Function:**
```typescript
const createDaytonaSandboxInBackground = async () => {
  try {
    // Destroy previous sandbox if exists
    const currentSandboxId = useBaseStore.getState().sandboxId;
    if (currentSandboxId) {
      await apiClient.delete(`/preview/destroy-sandbox/${currentSandboxId}`);
    }

    // Create new sandbox
    const response = await apiClient.post('/preview/create-sandbox');
    const { sandboxId, previewUrl } = response.data;

    // Store in base store
    useBaseStore.getState().setSandboxId(sandboxId);
    useBaseStore.getState().setPreviewUrl(previewUrl);
  } catch (error) {
    console.error('Failed to create Daytona sandbox:', error);
  }
};
```

### 3.2 Update Chat onFinish Handler

**File:** `apps/admin/src/features/chat/main.tsx`

**Modified createPreview Function:**
```typescript
const createPreview = async (files: Record<string, string>) => {
  try {
    const { sandboxId, previewUrl } = useBaseStore.getState();
    
    if (!sandboxId || !previewUrl) {
      throw new Error('No sandbox available');
    }

    // Health check with timeout
    await healthCheckWithTimeout(previewUrl, 15000);

    // Upload files to sandbox
    await apiClient.post('/preview/upload-files', {
      files,
      sandboxId,
    });

    // Set preview URL for display
    setPreviewUrl(previewUrl);
    return true;
  } catch (error) {
    console.error('Preview creation failed:', error);
    setBuildError(error.message);
    setShowErrorCard(true);
    return false;
  }
};
```

**Health Check Function:**
```typescript
const healthCheckWithTimeout = async (url: string, timeout: number) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(`${url}/api/health-check`, {
        method: 'GET',
        timeout: 2000,
      });
      
      if (response.ok) {
        return true;
      }
    } catch (error) {
      // Continue polling
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  throw new Error('Health check timeout');
};
```

## Phase 4: Cleanup and Optimization

### 4.1 Remove Unused Dependencies

**Package.json cleanup:**
- Remove any Fly.io specific dependencies
- Ensure @daytonaio/sdk is installed

### 4.2 Update Environment Variables

**Required Environment Variables:**
- `DAYTONA_API_KEY` (if needed)
- Remove all Fly.io related variables

### 4.3 Update Documentation

- Update README with Daytona setup instructions
- Remove Fly.io documentation references
- Add Daytona configuration guide

## Implementation Order

1. **Phase 1.1** - Remove Fly.io files and code
2. **Phase 1.2** - Remove database tables and run migrations
3. **Phase 2.1** - Update base store
4. **Phase 2.2** - Create Daytona service
5. **Phase 2.3** - Update preview controller
6. **Phase 2.4** - Update preview service
7. **Phase 3.1** - Update chat submit handler
8. **Phase 3.2** - Update chat onFinish handler
9. **Phase 4** - Cleanup and testing

## Key Benefits

- **Simplified Architecture**: Remove complex machine management
- **Faster Development**: No persistent machine tracking needed
- **Cost Effective**: Pay-per-use model with auto-destruction
- **Easier Maintenance**: Less code to maintain and debug
- **Better UX**: Simpler error handling and status management

## Risk Mitigation

- **Gradual Migration**: Implement Daytona alongside Fly.io initially
- **Fallback Strategy**: Keep Daytona test endpoint until full migration
- **Testing**: Comprehensive testing before removing Fly.io code
- **Monitoring**: Track performance and error rates during migration

## Success Metrics

- Preview creation time < 10 seconds
- 95% success rate for sandbox creation
- Reduced codebase complexity (fewer files and lines of code)
- Improved developer experience and maintainability
