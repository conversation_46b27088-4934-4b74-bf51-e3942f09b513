import { useGetPageStories } from '@/api/project-planning';
import { TreeDataItem } from '@/components/ui/tree';
import { env } from '@/config/env';
import { useGitService } from '@/hooks/use-git-service';
import { api, axiosErrorInterpreter } from '@/lib/api-client';
import { cookie } from '@/lib/cookie';
import { useBaseStore } from '@/store/base-store';
import { useChat } from '@ai-sdk/react';
import { File, Folder } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router';
import useWebSocket from 'react-use-websocket';
import { useGetOnboardingProgress } from '../onboarding/hooks/get-onboarding-progress';
import { useGetChatHistory } from './api/queries';
import { useValidateFiles } from './api/validation';
import { ChatHeader } from './components/chat-header';
import { ChatLeft } from './components/chat-left';
import { ChatRight } from './components/chat-right';
import { ChatSidebar } from './components/chat-sidebar';
import { useAutoStoryStart } from './hooks/use-auto-story-start';
import { beautifyCode } from './utils/cleanCode';
import {
  convertFilesToTreeData,
  convertTextToFiles,
  extractFileDeletions,
  extractFilePathsInOrder,
  filterEmptyFilesFromRecord,
  filterFilesForIgnored,
  getFileContentByPath,
  mergeFiles,
  modifyFilesForPreview,
  removeFiles,
} from './utils/convert';
import { getLanguage } from './utils/getLanguage';

interface UploadResponse {
  machineId: string;
  appName: string;
  previewUrl: string;
  wsUrl: string;
  filesCount: number;
  runtimeErrors?: Array<{
    message: string;
    level: string;
    timestamp: string;
    region: string;
    instanceId: string;
  }>;
  hasRuntimeErrors?: boolean;
}

//   {
//       type: 'connected';
//       message: string;
//     }
//   | {
//       type: 'build-status';
//       data: {
//         jobId: string;
//         status: 'processing' | 'completed' | 'failed'; // add more statuses if applicable
//         message: string;
//         progress: number;
//         error?: string;
//         logs?: string;
//       };
//     };

export default function Chat() {
  const [selectedFile, setSelectedFile] = useState({
    path: '',
    content: '',
  });
  const [treeData, setTreeData] = useState<TreeDataItem[] | TreeDataItem>([]);
  // Simple Record<string, string> format for all files
  const [gitFiles, setGitFiles] = useState<Record<string, string>>({});
  const token = cookie.get('access_token');
  const previewRef = useRef<HTMLIFrameElement>(null);
  const [view, setView] = useState<'code' | 'preview'>('code');
  const [language, setLanguage] = useState<string>('javascript');
  const [socketUrl, setSocketUrl] = useState('');
  const [previewUrl, setPreviewUrl] = useState('');
  // const [messageHistory, setMessageHistory] = useState<WebhookMessage[]>([]);
  // const parsed = JSON.parse(lastMessage.data) as WebhookMessage;
  const { lastMessage } = useWebSocket(socketUrl);
  const gitService = useGitService();
  const { moduleId, workspaceId } = useBaseStore((state) => state);
  const [searchParams] = useSearchParams();
  const pageIdFromQuery = searchParams.get('pageId');
  const getPageStories = useGetPageStories({
    workspaceId,
    moduleId: moduleId,
    pageId: pageIdFromQuery,
  });
  const pageRoute = getPageStories.data?.page.route;
  const validateFiles = useValidateFiles();
  // BuildProgress shared state
  const [error, setError] = useState<string | undefined>(undefined);
  const [logs, setLogs] = useState<string | undefined>(undefined);
  const [showLogs, setShowLogs] = useState(false);

  // OPTIMIZATION: Machine suspension management
  const [suspensionTimeout, setSuspensionTimeout] =
    useState<NodeJS.Timeout | null>(null);

  // Schedule machine suspension with cancellation capability
  const scheduleMachineSuspension = (appName: string, machineId: string) => {
    // Cancel any existing suspension timeout
    if (suspensionTimeout) {
      clearTimeout(suspensionTimeout);
    }

    // Schedule suspension after 2 minutes
    const newTimeout = setTimeout(
      async () => {
        try {
          await api.post(`/preview/suspend/${appName}/${machineId}`);
        } catch (error) {
          console.error(`Failed to suspend machine ${machineId}:`, error);
        }
      },
      2 * 60 * 1000,
    ); // 2 minutes

    setSuspensionTimeout(newTimeout);
  };

  // Fetch chat history for the current page
  const chatHistory = useGetChatHistory(workspaceId, moduleId, pageIdFromQuery);

  // Fetch onboarding data for context
  const getOnboardingProgress = useGetOnboardingProgress();
  const onboardingData = getOnboardingProgress.data?.data;

  // MIGRATION: Helper function to save files to Git repository and create a commit
  const saveFilesToGitRepository = async (
    gitFiles: Record<string, string>,
    messages: { role: string; content: string; id: string }[],
  ) => {
    // Track files that were written
    const writtenFiles: string[] = [];

    // MIGRATION: Process git files directly instead of tree structure
    for (const [filePath, fileContent] of Object.entries(gitFiles)) {
      if (gitService?.writeFile) {
        const rawNewContent = fileContent;

        // FILTER: Skip empty files - they should be deleted, not written
        if (rawNewContent.trim() === '') {
          continue;
        }
        let shouldWriteFile = false;

        if (!gitService.readFile) {
          // If readFile capability doesn't exist on gitService (e.g., service not fully initialized),
          // fallback to assuming a write is needed to be safe, or handle error appropriately.
          // For now, let's assume it means we should write if we intend to change.
          shouldWriteFile = true;
        } else {
          try {
            const rawExistingContent = await gitService.readFile({
              relativeFilePath: filePath,
            });

            // Normalize line endings on RAW content for comparison
            const normalizedRawNew = rawNewContent
              .replace(/\r\n/g, '\n')
              .replace(/\r/g, '\n');
            const normalizedRawExisting = rawExistingContent
              .replace(/\r\n/g, '\n')
              .replace(/\r/g, '\n');

            if (normalizedRawExisting !== normalizedRawNew) {
              shouldWriteFile = true;
            }
          } catch (error) {
            // If readFile throws an error (e.g., file not found - ENOENT),
            // it's a new file, so we should write.
            console.error(error);
            shouldWriteFile = true;
          }
        }

        if (shouldWriteFile) {
          try {
            // Always write the beautified version of the new content
            const beautifiedContentToWrite = beautifyCode(
              rawNewContent,
              filePath,
            );
            await gitService.writeFile({
              relativeFilePath: filePath,
              content: beautifiedContentToWrite,
            });
            writtenFiles.push(filePath); // Only count if actually written
          } catch (writeError) {
            console.error(`Failed to write file: ${filePath}`, writeError);
          }
        }
      }
    }

    // Create a commit if files were written
    if (writtenFiles.length > 0 && gitService?.createGitCommit) {
      try {
        // Extract the first line of the user's message as the commit message
        const userMessage = messages.find((msg) => msg.role === 'user');
        const userMessageContent = userMessage?.content || '';
        const commitMessage =
          userMessageContent.split('\n')[0].trim() || 'Update from chat';

        // Create the commit
        await gitService?.createGitCommit({ message: commitMessage });
      } catch (error) {
        console.error('Chat: Failed to create commit:', error);
        // Error is already logged in the createCommit function
      }
    }

    return writtenFiles;
  };

  // Helper function to delete files from Git repository
  const deleteFilesFromGitRepository = async (
    filesToDelete: string[],
  ): Promise<string[]> => {
    const deletedFiles: string[] = [];

    if (!gitService?.deleteFile) {
      console.warn('Git service deleteFile method not available');
      return deletedFiles;
    }

    for (const filePath of filesToDelete) {
      try {
        await gitService.deleteFile({
          relativeFilePath: filePath,
        });
        deletedFiles.push(filePath);
      } catch (error) {
        console.error(`Failed to delete file: ${filePath}`, error);
        // Continue with other files even if one fails
      }
    }

    return deletedFiles;
  };

  // Helper function to format runtime errors for AI feedback
  const formatRuntimeErrorsForAI = (
    runtimeErrors: UploadResponse['runtimeErrors'],
  ) => {
    if (!runtimeErrors || runtimeErrors.length === 0) {
      return '';
    }

    const errorSummaries = runtimeErrors.map((error, index) => {
      const timestamp = new Date(error.timestamp).toLocaleString();
      return `${index + 1}. [${timestamp}] [${error.level.toUpperCase()}] ${error.message}`;
    });

    const summary =
      runtimeErrors.length === 1
        ? `Found 1 runtime error in the preview application`
        : `Found ${runtimeErrors.length} runtime errors in the preview application`;

    const formattedLogs = runtimeErrors
      .map((error) => {
        const timestamp = new Date(error.timestamp).toLocaleString();
        return `[${timestamp}] [${error.level.toUpperCase()}] [${error.region}] ${error.message}`;
      })
      .join('\n');

    return `<fullstackfoxError>
${summary}:

${errorSummaries.join('\n')}
</fullstackfoxError>

The injected code is causing runtime errors in the preview application. Here are the error details from the application logs:

${formattedLogs}

Please analyze these runtime errors and fix the code to resolve all issues. Focus on:
1. Undefined variables or functions
2. Import/export issues
3. Syntax errors
4. Type errors
5. Missing dependencies or modules

The preview application should run without any runtime errors.`;
  };

  // Helper function to handle preview creation and error checking
  const createPreview = async (
    files: Record<string, string>,
    context: string = '',
  ): Promise<boolean> => {
    try {
      // Clear any existing suspension timeout before creating new preview
      if (suspensionTimeout) {
        clearTimeout(suspensionTimeout);
        setSuspensionTimeout(null);
      }

      // Modify files for preview mode (package.json and page route)
      const modifiedFiles = modifyFilesForPreview(files, pageRoute);

      // Update the preview - send the files directly
      const livePreviewResult = await api.post(
        '/preview/upload',
        modifiedFiles,
      );
      const response = livePreviewResult as unknown as UploadResponse;
      const previewUrl = response.previewUrl;

      setPreviewUrl(previewUrl);
      setSocketUrl(response.wsUrl);

      // Set iframe src directly for immediate preview
      if (previewRef.current) {
        previewRef.current.src = previewUrl;
      }

      setView('preview');

      // Schedule machine suspension after successful preview creation
      if (response.machineId && response.appName) {
        scheduleMachineSuspension(response.appName, response.machineId);
      }

      // Check for runtime errors after preview creation
      if (
        response.hasRuntimeErrors &&
        response.runtimeErrors &&
        response.runtimeErrors.length > 0
      ) {
        console.warn(
          `Runtime errors detected in preview${context}: ${response.runtimeErrors.length} errors`,
        );

        // Format runtime errors for AI feedback
        const runtimeErrorMessage = formatRuntimeErrorsForAI(
          response.runtimeErrors,
        );

        // Send runtime errors back to AI for fixing
        await append({
          role: 'user',
          content: runtimeErrorMessage,
        });
        return false; // Indicate that there were runtime errors
      }

      return true; // Success, no runtime errors
    } catch (error) {
      console.error(
        `Failed to compile React code for preview${context}:`,
        error,
      );
      return false; // Preview failed
    }
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    stop,
    status,
    append,
  } = useChat({
    api: `${env.API_URL}/chat/workspaces/${workspaceId}/modules/${moduleId}/pages/${pageIdFromQuery}`,
    initialMessages: chatHistory.data || [],
    body: {
      // MIGRATION: Use new gitFiles state with filtering
      currentFiles: filterFilesForIgnored(gitFiles),
      onboardingData: onboardingData || null,
    },
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    onError: (error) => {
      console.error('error', error);
      const axiosError = {
        response: {
          status: JSON.parse(error.message)?.statusCode,
          data: JSON.parse(error.message),
        },
      };
      axiosErrorInterpreter(axiosError, handleSubmit);
    },
    onFinish: async (message, { finishReason }) => {
      try {
        // Extract files from the current AI response message directly to files
        const newFiles = convertTextToFiles(message.content);

        // Extract file deletions from the current AI response message
        const fileDeletions = extractFileDeletions(message.content);

        // Check for empty files and treat them as deletions
        const emptyFilesToDelete: string[] = [];
        const cleanedFiles: Record<string, string> = {};

        Object.entries(newFiles).forEach(([filePath, content]) => {
          if (typeof content === 'string' && content.trim() === '') {
            // Empty file - mark for deletion
            emptyFilesToDelete.push(filePath);
          } else if (typeof content === 'string') {
            // Non-empty file - keep it
            cleanedFiles[filePath] = content;
          }
        });

        // Add empty files to deletion list
        if (emptyFilesToDelete.length > 0) {
          fileDeletions.filesToDelete.push(...emptyFilesToDelete);
        }

        // Check if this might be a continuation scenario where the final message has no files
        // but previous assistant messages in the sequence might have files
        let finalFiles = cleanedFiles;

        if (
          Object.keys(cleanedFiles).length === 0 &&
          finishReason !== 'length'
        ) {
          // This could be a continuation completion - check recent assistant messages for files
          // Look back through the last few assistant messages to collect all files
          const recentAssistantMessages = messages
            .filter((msg) => msg.role === 'assistant')
            .slice(-5); // Check last 5 assistant messages

          let accumulatedFiles: Record<string, string> = {};

          for (const assistantMsg of recentAssistantMessages) {
            // Process files from recent messages
            const msgFiles = convertTextToFiles(assistantMsg.content);
            if (Object.keys(msgFiles).length > 0) {
              accumulatedFiles = mergeFiles(accumulatedFiles, msgFiles);
            }
          }

          // If we found files in recent messages, use them
          if (Object.keys(accumulatedFiles).length > 0) {
            finalFiles = accumulatedFiles;
          }
        }

        // Handle file deletions FIRST - before processing new files
        if (fileDeletions.filesToDelete.length > 0) {
          // Delete files from Git repository
          const deletedFiles = await deleteFilesFromGitRepository(
            fileDeletions.filesToDelete,
          );

          // Remove deleted files from gitFiles state
          setGitFiles((currentGitFiles) => {
            return removeFiles(currentGitFiles, deletedFiles);
          });
        }

        // Process new/updated files if any exist
        if (Object.keys(finalFiles).length > 0) {
          // Apply final filter to remove any empty files that might have slipped through
          const filteredFinalFiles = filterEmptyFilesFromRecord(finalFiles);

          // Save files to Git repository using new files format
          await saveFilesToGitRepository(filteredFinalFiles, messages);

          // Update gitFiles state
          setGitFiles((currentGitFiles) => {
            return mergeFiles(currentGitFiles, filteredFinalFiles);
          });

          // Validate the generated code after saving
          const validationResult = await validateFiles.mutateAsync({
            files: filteredFinalFiles,
          });

          if (!validationResult.isValid && validationResult.errors.length > 0) {
            // Format validation errors for better UX
            const formatValidationErrors = (errors: any[]) => {
              const errorSummaries = errors.map((error, index) => {
                const file = error.filePath || 'Unknown file';
                const line = error.line ? ` (line ${error.line})` : '';
                const column = error.column ? `:${error.column}` : '';
                const message = error.message || 'Unknown error';
                const source = error.source ? ` [${error.source}]` : '';

                // Truncate long error messages for UI display
                const truncatedMessage =
                  message.length > 100
                    ? `${message.substring(0, 100)}...`
                    : message;

                return `${index + 1}. ${file}${line}${column}: ${truncatedMessage}${source}`;
              });

              // Create a user-friendly summary for UI
              const summary =
                errors.length === 1
                  ? `Found 1 validation error in the generated code`
                  : `Found ${errors.length} validation errors in the generated code`;

              // Full error details for AI processing
              const fullErrorDetails = JSON.stringify(errors, null, 2);

              return {
                summary,
                errorSummaries,
                fullErrorDetails,
              };
            };

            const { summary, errorSummaries, fullErrorDetails } =
              formatValidationErrors(validationResult.errors);

            await append({
              role: 'user',
              content: `<fullstackfoxError>
${summary}:

${errorSummaries.join('\n')}
</fullstackfoxError>

The generated code has validation errors that need to be fixed. Here are the complete error details:

${fullErrorDetails}

Please analyze these errors and fix the code to resolve all validation issues before proceeding.`,
            });
            return; // Stop processing if there are validation errors
          }

          // Only attempt preview operations if finish reason is not 'length'
          if (finishReason !== 'length') {
            // Get the current merged files for preview
            let currentMergedFiles: Record<string, string> = {};
            setGitFiles((currentGitFiles) => {
              currentMergedFiles = mergeFiles(
                currentGitFiles,
                filteredFinalFiles,
              );
              return currentGitFiles; // Don't update state here, just read
            });

            // Create preview and handle any runtime errors
            const previewSuccess = await createPreview(currentMergedFiles);
            if (!previewSuccess) {
              return; // Stop processing if there were runtime errors
            }
          }
        }

        // Handle the case where there are only deletions and no new files
        // We still need to update the preview if files were deleted
        if (
          Object.keys(finalFiles).length === 0 &&
          fileDeletions.filesToDelete.length > 0 &&
          finishReason !== 'length'
        ) {
          // Get the current files state for preview
          let currentFilesForPreview: Record<string, string> = {};
          setGitFiles((currentGitFiles) => {
            currentFilesForPreview = currentGitFiles;
            return currentGitFiles; // No change, just reading
          });

          // Create preview and handle any runtime errors
          const previewSuccess = await createPreview(
            currentFilesForPreview,
            ' after deletions',
          );
          if (!previewSuccess) {
            return; // Stop processing if there were runtime errors
          }
        }

        // Handle continuation after processing files
        if (finishReason === 'length') {
          await append({
            role: 'user',
            content:
              'The previous response was incomplete due to length limitations. Please continue generating the response from where you left off.',
          });
          return;
        }
      } catch (error) {
        console.error('Failed to process AI response or create commit:', error);
      }
    },
  });

  // Note: Auto-resolve build failures is now handled manually via error resolution card
  // This prevents interrupting streaming and gives users control over when to resolve errors

  // UseEffect to update the git files and tree data to the default git files
  useEffect(() => {
    const handleSetDefaultGitFiles = async () => {
      if (
        gitService?.getAllProjectFiles &&
        Object.keys(gitFiles).length === 0
      ) {
        // Only run if gitFiles is empty
        const initialGitFiles = await gitService.getAllProjectFiles();
        if (initialGitFiles && Object.keys(initialGitFiles).length > 0) {
          // MIGRATION: Use new gitFiles state directly
          setGitFiles(initialGitFiles);

          // MIGRATION: Use new convertFilesToTreeData function
          const initialTreeData = convertFilesToTreeData(
            initialGitFiles,
            Folder,
            File,
          );
          setTreeData(initialTreeData);

          // FileSystemTree is no longer needed - using simple Record<string, string>

          // Optionally, set an initial selected file
          // For example, select the first file if available
          const firstFilePath = Object.keys(initialGitFiles)[0];
          if (firstFilePath) {
            const firstFileContent = initialGitFiles[firstFilePath];
            setSelectedFile({
              path: firstFilePath,
              content: beautifyCode(firstFileContent, firstFilePath),
            });
            setLanguage(getLanguage(firstFilePath));
          }
        }
      }
    };

    handleSetDefaultGitFiles();
  }, [gitService, gitFiles]); // MIGRATION: Changed dependency from fileTree to gitFiles

  // Auto-start logic for initializing conversation with stories
  useAutoStoryStart({
    chatHistory,
    storiesData: getPageStories,
    pageId: pageIdFromQuery,
    isEnabled: true,
    append,
    messages,
    status,
  });

  useEffect(() => {
    if (status !== 'streaming') return; // Only update when streaming
    if (messages.length === 0) return; // No messages yet
    const latestMessage = messages[messages.length - 1];

    // MIGRATION: Convert the latest message directly to files
    const newFiles = convertTextToFiles(latestMessage.content);

    // MIGRATION: Merge the new files with the existing ones
    const mergedFiles = mergeFiles(gitFiles, newFiles);

    // MIGRATION: Generate tree data from the merged files
    const updatedTreeData = convertFilesToTreeData(mergedFiles, Folder, File);

    // FileSystemTree is no longer needed

    // Get files being edited in the order they appear in the message
    const newFilePathsInOrder = extractFilePathsInOrder(latestMessage.content);

    let currentFile: TreeDataItem | null = null;

    if (newFilePathsInOrder.length > 0) {
      // If there are files being edited, show the last one being edited (most recent)
      const targetFilePath =
        newFilePathsInOrder[newFilePathsInOrder.length - 1];

      // Find the file in the tree
      const findFileInTree = (
        items: TreeDataItem[] | TreeDataItem,
        path: string,
      ): TreeDataItem | null => {
        const itemsArray = Array.isArray(items) ? items : [items];

        for (const item of itemsArray) {
          if (item.path === path) {
            return item;
          }
          if (item.children) {
            const found = findFileInTree(item.children, path);
            if (found) return found;
          }
        }
        return null;
      };

      currentFile = findFileInTree(updatedTreeData, targetFilePath);
    } else {
      // No new files, use the original logic to get the last file
      const lastItem =
        Array.isArray(updatedTreeData) && updatedTreeData.length > 0
          ? updatedTreeData[updatedTreeData.length - 1]
          : (updatedTreeData as TreeDataItem | null);

      const lastItemChildren = lastItem?.children;
      const lastChildIndex = lastItemChildren?.length
        ? lastItemChildren.length - 1
        : 0;

      currentFile = lastItem
        ? lastItemChildren && lastItemChildren.length > 0
          ? lastItemChildren[lastChildIndex]
          : lastItem
        : null;
    }

    // MIGRATION: Try to find the file in the new files first, then fall back to merged
    let rawContent = getFileContentByPath(newFiles, currentFile?.path ?? '');

    // If the file isn't in the new files, look in the merged files
    if (rawContent === null) {
      rawContent =
        getFileContentByPath(mergedFiles, currentFile?.path ?? '') ?? '';
    }

    setSelectedFile({
      path: currentFile?.path ?? '',
      content: beautifyCode(rawContent, currentFile?.path ?? ''),
    });
    const fileExtension = currentFile?.path;

    // Update state with the merged data
    setGitFiles(mergedFiles);
    setTreeData(updatedTreeData);
    setLanguage(getLanguage(fileExtension));
  }, [messages, gitFiles, status]); // MIGRATION: Changed dependency from fileTree to gitFiles

  // Show loading state while fetching chat history
  if (chatHistory.isLoading) {
    return (
      <div className="flex h-screen bg-black-200 items-center justify-center">
        <div>Loading chat history...</div>
      </div>
    );
  }

  // Check for required parameters
  if (!workspaceId || !moduleId || !pageIdFromQuery) {
    return (
      <div className="flex h-screen bg-black-200 items-center justify-center">
        <div>Missing required parameters (workspace, module, or page ID)</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-black-200">
      <ChatSidebar />

      <div className="flex flex-col gap-0 w-full ">
        <ChatHeader />

        <div className="flex gap-2 w-full h-screen bg-black-200">
          <ChatLeft
            status={status}
            messages={messages}
            input={input}
            handleInputChange={handleInputChange}
            handleStop={stop}
            setView={setView}
            handleChatSubmit={handleSubmit}
            lastMessage={lastMessage}
            append={append}
            setError={setError}
            setLogs={setLogs}
            setShowLogs={setShowLogs}
          />

          <ChatRight
            treeData={treeData}
            gitFiles={gitFiles}
            setSelectedFile={setSelectedFile}
            setLanguage={setLanguage}
            previewRef={previewRef}
            language={language}
            selectedFile={selectedFile}
            view={view}
            setView={setView}
            lastMessage={lastMessage}
            previewUrl={previewUrl}
            error={error}
            setError={setError}
            logs={logs}
            setLogs={setLogs}
            showLogs={showLogs}
            setShowLogs={setShowLogs}
          />
        </div>
      </div>
    </div>
  );
}
