import { AutoResizeTextarea } from '@/components/ui/auto-resize-textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { api } from '@/lib/api-client';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { useBaseStore } from '@/store/base-store';
import { useChat } from '@ai-sdk/react';
import {
  ArrowDown,
  ArrowUp,
  Code,
  ImageIcon,
  Loader,
  Square,
  X,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { ErrorResolutionCard } from './error-resolution-card';
import { SupabaseConnectionCard } from './supabase-connection-card';

type UIMessage = ReturnType<typeof useChat>['messages'][number];
type UIChatSubmit = ReturnType<typeof useChat>['handleSubmit'];

export const ChatLeft = ({
  messages,
  input,
  handleInputChange,
  handleChatSubmit,
  handleStop,
  status,
  setView,
  lastMessage,
  append,
  setError,
  setLogs,
  setShowLogs,
}: {
  messages: UIMessage[];
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleStop: () => void;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  handleChatSubmit: UIChatSubmit;
  setView: (value: React.SetStateAction<'code' | 'preview'>) => void;
  lastMessage: any; // WebSocket message
  append: (message: {
    role: 'user' | 'assistant';
    content: string;
  }) => Promise<string | null | undefined>;
  setError: React.Dispatch<React.SetStateAction<string | undefined>>;
  setLogs: React.Dispatch<React.SetStateAction<string | undefined>>;
  setShowLogs: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [files, setFiles] = useState<FileList | undefined>(undefined);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = React.useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [filePreviews, setFilePreviews] = useState<
    { url: string; file: File }[]
  >([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isStreaming = status === 'streaming' || status === 'submitted';
  const {
    showSupabaseCard,
    setShowSupabaseCard,
    showErrorCard,
    setShowErrorCard,
  } = useBaseStore((state) => state);
  const [buildError, setBuildError] = useState<string>(''); // Local state for build error
  const [lastProcessedBuildMessageId, setLastProcessedBuildMessageId] =
    useState<string | null>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle build errors after streaming is complete
  useEffect(() => {
    if (!isStreaming && lastMessage) {
      try {
        const parsed = JSON.parse(lastMessage.data);

        if (
          parsed?.type === 'build-status' &&
          parsed?.data?.status === 'failed' &&
          parsed?.data?.error
        ) {
          // Create a unique identifier for this build message
          const messageId = `${parsed.data.error}-${parsed.data.logs?.length || 0}`;

          // Only process if we haven't already processed this exact message
          if (messageId !== lastProcessedBuildMessageId) {
            const buildError = parsed.data.error;
            const buildLogs = parsed.data.logs;

            // Clean up the logs by removing Docker stream control characters
            const cleanLogs = buildLogs
              ? buildLogs
                  .split('')
                  .filter((char: string) => {
                    const code = char.charCodeAt(0);
                    // Keep printable characters, newlines, and tabs
                    return (
                      (code >= 32 && code <= 126) ||
                      code === 10 ||
                      code === 13 ||
                      code === 9
                    );
                  })
                  .join('')
              : '';

            // Extract relevant error information from logs
            const errorLines = cleanLogs
              .split('\n')
              .filter(
                (line: string) =>
                  line.includes('Failed to compile') ||
                  line.includes('Module not found') ||
                  line.includes("Can't resolve") ||
                  line.includes('Error:') ||
                  line.includes('TypeError:') ||
                  line.includes('SyntaxError:') ||
                  line.includes('ReferenceError:') ||
                  line.includes('Build failed') ||
                  line.includes('webpack errors') ||
                  line.includes('Import trace'),
              )
              .slice(0, 10); // Limit to first 10 relevant error lines

            const errorContext =
              errorLines.length > 0
                ? `${buildError}\n\nDetailed error information:\n${errorLines.join('\n')}`
                : buildError;

            setBuildError(errorContext);
            setShowErrorCard(true);
            setLastProcessedBuildMessageId(messageId);
          }
        }

        // Check for Supabase connection success
        if (
          parsed?.type === 'build-status' &&
          parsed?.data?.status === 'completed'
        ) {
          setShowSupabaseCard(true);
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    }
  }, [
    isStreaming,
    lastMessage,
    lastProcessedBuildMessageId,
    setShowErrorCard,
    setShowSupabaseCard,
  ]);

  // Function to scroll to bottom
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Handle scroll events to show/hide the scroll button
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const viewport = e.currentTarget;

    if (viewport) {
      const { scrollTop, scrollHeight, clientHeight } = viewport;
      const isScrolledUp = scrollHeight - scrollTop - clientHeight > 100;
      setShowScrollButton(isScrolledUp);
    }
  };

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      // Check if adding new files would exceed the maximum limit
      const MAX_FILES = 3;
      const totalFiles = filePreviews.length + event.target.files.length;

      if (totalFiles > MAX_FILES) {
        toast.error(`You can upload a maximum of ${MAX_FILES} images.`);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      // Check file size - 1MB limit
      const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1MB in bytes

      const validFiles: File[] = [];
      let hasInvalidSize = false;

      Array.from(event.target.files).forEach((file) => {
        if (file.size > MAX_FILE_SIZE) {
          hasInvalidSize = true;
        } else {
          validFiles.push(file);
        }
      });

      if (hasInvalidSize) {
        toast.error(
          'File size exceeds 1MB limit. Please select a smaller file.',
        );

        // If we have some valid files, continue with those
        if (validFiles.length === 0) {
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
          return;
        }
      }

      // Create a new FileList with only valid files
      if (validFiles.length > 0) {
        const dataTransfer = new DataTransfer();

        // Add existing files first
        filePreviews.forEach((preview) => {
          dataTransfer.items.add(preview.file);
        });

        // Then add new files
        validFiles.forEach((file) => {
          dataTransfer.items.add(file);
        });

        const newPreviews = [
          ...filePreviews,
          ...validFiles.map((file) => ({
            url: URL.createObjectURL(file),
            file,
          })),
        ];

        setFilePreviews(newPreviews);
        setFiles(dataTransfer.files);
      }
    }
  };

  // Remove a file from the selection
  const removeFile = (indexToRemove: number) => {
    // Release the object URL to avoid memory leaks
    URL.revokeObjectURL(filePreviews[indexToRemove].url);

    const updatedPreviews = filePreviews.filter(
      (_, index) => index !== indexToRemove,
    );
    setFilePreviews(updatedPreviews);

    // Create a new FileList-like object with the remaining files
    if (updatedPreviews.length === 0) {
      setFiles(undefined);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } else {
      const dataTransfer = new DataTransfer();
      updatedPreviews.forEach((preview) => {
        dataTransfer.items.add(preview.file);
      });
      setFiles(dataTransfer.files);
    }
  };

  // Create Daytona sandbox in background during chat submit
  const createDaytonaSandboxInBackground = async () => {
    try {
      // Destroy previous sandbox if exists
      const currentSandboxId = useBaseStore.getState().sandboxId;
      if (currentSandboxId) {
        await api.delete(`/preview/destroy-sandbox/${currentSandboxId}`);
      }

      // Create new sandbox
      const response = await api.post('/preview/create-sandbox');
      const { sandboxId, previewUrl } = response.data;

      // Store in base store
      useBaseStore.getState().setSandboxId(sandboxId);
      useBaseStore.getState().setPreviewUrl(previewUrl);

      console.log(`Daytona sandbox created: ${sandboxId}`);
    } catch (error) {
      console.error('Failed to create Daytona sandbox:', error);
      // Background sandbox creation failed - will handle in onFinish
    }
  };

  const handleSubmit = () => {
    setBuildError('');
    setShowErrorCard(false);
    setView('code');
    setLogs('');
    setError(undefined);
    setShowLogs(false);

    // Start Daytona sandbox creation in background (no await needed)
    createDaytonaSandboxInBackground();

    handleChatSubmit(undefined, {
      experimental_attachments: files,
    });

    setFiles(undefined);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    filePreviews.forEach((preview) => {
      URL.revokeObjectURL(preview.url);
    });

    setFilePreviews([]);
  };

  // Handle error resolution
  const handleResolveError = async () => {
    if (buildError) {
      const errorMessage = `The build process failed with the following error: ${buildError}\n\nPlease analyze the error and fix the code to resolve this build failure. Focus on the specific modules or dependencies that couldn't be resolved.`;

      try {
        await append({
          role: 'user',
          content: errorMessage,
        });
      } catch (error) {
        console.error('Failed to send error resolution message:', error);
      }
    }
  };

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.url);
      });
    };
  }, [filePreviews]);

  return (
    <div className="flex flex-col h-[88vh] px-6 mx-auto relative w-[45%]">
      <ScrollArea
        type="auto"
        className="flex flex-col h-[75vh] relative"
        ref={scrollAreaRef}
        onScroll={handleScroll}
      >
        {messages.map((message) => {
          if (message.role === 'user') {
            return (
              <div key={message.id} className="mb-6 w-[60%] ml-auto">
                <div className="bg-[#131921] rounded-2xl p-3 max-w-2xl text-[#F4F4F5] text-[15px] font-inter">
                  <div className="leading-relaxed">
                    {message.parts.map((part, partIndex) => {
                      if (part.type !== 'text') return null;

                      // Filter out fullstackfox tags from user messages too
                      const lines = part.text.split('\n');
                      const filteredContent = [];
                      let insideArtifact = false;
                      let insideUserStories = false;
                      let insidePageDetails = false;

                      for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];

                        // Check for artifact tags (both fullstackfoxArtifact and fullstackfoxAction)
                        if (
                          line.includes('<fullstackfoxArtifact') ||
                          line.includes('<fullstackfoxAction')
                        ) {
                          insideArtifact = true;
                          continue;
                        }

                        if (
                          line.includes('</fullstackfoxArtifact>') ||
                          line.includes('</fullstackfoxAction>')
                        ) {
                          insideArtifact = false;
                          continue;
                        }

                        // Check for user stories tags
                        if (line.includes('<fullstackfoxUserStories>')) {
                          insideUserStories = true;
                          continue;
                        }

                        if (line.includes('</fullstackfoxUserStories>')) {
                          insideUserStories = false;
                          continue;
                        }

                        // Check for page details tags
                        if (line.includes('<fullstackfoxPageDetails>')) {
                          insidePageDetails = true;
                          continue;
                        }

                        if (line.includes('</fullstackfoxPageDetails>')) {
                          insidePageDetails = false;
                          continue;
                        }

                        // Only include content if not inside any ignored tags
                        if (
                          !insideArtifact &&
                          !insideUserStories &&
                          !insidePageDetails
                        ) {
                          filteredContent.push(line);
                        }
                      }

                      const cleanedContent = filteredContent
                        .filter((line) => line.trim() !== '') // Remove empty lines
                        .join('\n')
                        .trim(); // Remove leading/trailing whitespace

                      return (
                        <div key={`${message.id}-part-${partIndex}`}>
                          {cleanedContent && cleanedContent.length > 0
                            ? cleanedContent
                            : ''}
                        </div>
                      );
                    })}
                  </div>

                  {message.experimental_attachments && (
                    <div className="flex items-center gap-2 mt-4">
                      {message.experimental_attachments
                        ?.filter((attachment) =>
                          attachment.contentType?.startsWith('image/'),
                        )
                        .map((attachment, index) => (
                          <img
                            key={`${message.id}-${index}`}
                            src={attachment.url}
                            alt={attachment.name}
                            className="w-32 h-32 rounded-md"
                          />
                        ))}
                    </div>
                  )}
                </div>
              </div>
            );
          }

          // AI Message with CRED-like design
          return (
            <div
              key={message.id}
              className="mb-6 w-[90%] mr-auto overflow-clip text-[15px] font-inter"
            >
              <div className="bg-[#21262d] rounded-3xl p-4 max-w-2xl text-white">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 rounded-full border border-gray-600 flex items-center justify-center">
                    <Code className="w-4 h-4 text-gray-400" />
                  </div>
                  <span className="text-gray-200 font-medium">
                    Full Stack Fox
                  </span>
                </div>

                <div className="space-y-4">
                  {message.parts.map((part, partIndex) => {
                    if (part.type !== 'text') return null;

                    // Process text to filter out artifact tags and user stories/page details, and extract special tags
                    const lines = part.text.split('\n');
                    const filteredContent = [];
                    const specialMessages = [];
                    let insideArtifact = false;
                    let insideUserStories = false;
                    let insidePageDetails = false;
                    let insideSuccess = false;
                    let insideInfo = false;
                    let insideWarning = false;
                    let insideNote = false;
                    let insideQuery = false;
                    let insideResponse = false;
                    let insideExample = false;
                    let insideError = false;
                    let currentSuccessContent = [];
                    let currentInfoContent = [];
                    let currentNoteContent = [];
                    let currentQueryContent = [];
                    let currentResponseContent = [];
                    let currentExampleContent = [];
                    let currentErrorContent = [];
                    let hasFilterableTags = false; // Track if we found any filterable tags

                    for (let i = 0; i < lines.length; i++) {
                      const line = lines[i];

                      // Check for artifact tags (both fullstackfoxArtifact and fullstackfoxAction)
                      if (
                        line.includes('<fullstackfoxArtifact') ||
                        line.includes('<fullstackfoxAction')
                      ) {
                        insideArtifact = true;
                        hasFilterableTags = true;
                        continue;
                      }

                      if (
                        line.includes('</fullstackfoxArtifact>') ||
                        line.includes('</fullstackfoxAction>')
                      ) {
                        insideArtifact = false;
                        continue;
                      }

                      // Check for user stories tags
                      if (line.includes('<fullstackfoxUserStories>')) {
                        insideUserStories = true;
                        hasFilterableTags = true;
                        continue;
                      }

                      if (line.includes('</fullstackfoxUserStories>')) {
                        insideUserStories = false;
                        continue;
                      }

                      // Check for page details tags
                      if (line.includes('<fullstackfoxPageDetails>')) {
                        insidePageDetails = true;
                        hasFilterableTags = true;
                        continue;
                      }

                      if (line.includes('</fullstackfoxPageDetails>')) {
                        insidePageDetails = false;
                        continue;
                      }

                      // Check for success tags
                      if (line.includes('<fullstackfoxSuccess>')) {
                        insideSuccess = true;
                        currentSuccessContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxSuccess>')) {
                        insideSuccess = false;
                        if (currentSuccessContent.length > 0) {
                          specialMessages.push({
                            type: 'success',
                            content: currentSuccessContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for info tags
                      if (line.includes('<fullstackfoxInfo>')) {
                        insideInfo = true;
                        currentInfoContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxInfo>')) {
                        insideInfo = false;
                        if (currentInfoContent.length > 0) {
                          specialMessages.push({
                            type: 'info',
                            content: currentInfoContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for warning tags (ignore content)
                      if (line.includes('<fullstackfoxWarning>')) {
                        insideWarning = true;
                        hasFilterableTags = true;
                        continue;
                      }

                      if (line.includes('</fullstackfoxWarning>')) {
                        insideWarning = false;
                        continue;
                      }

                      // Check for note tags
                      if (line.includes('<fullstackfoxNote>')) {
                        insideNote = true;
                        currentNoteContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxNote>')) {
                        insideNote = false;
                        if (currentNoteContent.length > 0) {
                          specialMessages.push({
                            type: 'note',
                            content: currentNoteContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for query tags
                      if (line.includes('<fullstackfoxQuery>')) {
                        insideQuery = true;
                        currentQueryContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxQuery>')) {
                        insideQuery = false;
                        if (currentQueryContent.length > 0) {
                          specialMessages.push({
                            type: 'query',
                            content: currentQueryContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for response tags
                      if (line.includes('<fullstackfoxResponse>')) {
                        insideResponse = true;
                        currentResponseContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxResponse>')) {
                        insideResponse = false;
                        if (currentResponseContent.length > 0) {
                          specialMessages.push({
                            type: 'response',
                            content: currentResponseContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for example tags
                      if (line.includes('<fullstackfoxExample>')) {
                        insideExample = true;
                        currentExampleContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxExample>')) {
                        insideExample = false;
                        if (currentExampleContent.length > 0) {
                          specialMessages.push({
                            type: 'example',
                            content: currentExampleContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Check for error tags
                      if (line.includes('<fullstackfoxError>')) {
                        insideError = true;
                        currentErrorContent = [];
                        continue;
                      }

                      if (line.includes('</fullstackfoxError>')) {
                        insideError = false;
                        if (currentErrorContent.length > 0) {
                          specialMessages.push({
                            type: 'error',
                            content: currentErrorContent.join('\n').trim(),
                          });
                        }
                        continue;
                      }

                      // Collect content inside special tags
                      if (insideSuccess) {
                        currentSuccessContent.push(line);
                        continue;
                      }

                      if (insideInfo) {
                        currentInfoContent.push(line);
                        continue;
                      }

                      if (insideNote) {
                        currentNoteContent.push(line);
                        continue;
                      }

                      if (insideQuery) {
                        currentQueryContent.push(line);
                        continue;
                      }

                      if (insideResponse) {
                        currentResponseContent.push(line);
                        continue;
                      }

                      if (insideExample) {
                        currentExampleContent.push(line);
                        continue;
                      }

                      if (insideError) {
                        currentErrorContent.push(line);
                        continue;
                      }

                      // Only include content if not inside any ignored tags
                      if (
                        !insideArtifact &&
                        !insideUserStories &&
                        !insidePageDetails &&
                        !insideWarning
                      ) {
                        filteredContent.push(line);
                      }
                    }

                    return (
                      <div key={`${message.id}-part-${partIndex}`}>
                        {(() => {
                          const cleanedContent = filteredContent
                            .filter((line) => line.trim() !== '') // Remove empty lines
                            .join('\n')
                            .trim(); // Remove leading/trailing whitespace

                          return cleanedContent && cleanedContent.length > 0 ? (
                            <div className="text-gray-200 leading-relaxed whitespace-pre-wrap">
                              {cleanedContent}
                            </div>
                          ) : null;
                        })()}

                        {/* Display special messages */}
                        {specialMessages.map((specialMsg, msgIndex) => (
                          <div key={`special-${msgIndex}`} className="mt-4">
                            {specialMsg.type === 'success' && (
                              <div className="bg-green-900/30 border border-green-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                  <span className="text-green-300 font-medium text-sm">
                                    Success
                                  </span>
                                </div>
                                <div className="text-green-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'info' && (
                              <div className="bg-blue-900/30 border border-blue-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                  <span className="text-blue-300 font-medium text-sm">
                                    Info
                                  </span>
                                </div>
                                <div className="text-blue-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'note' && (
                              <div className="bg-yellow-900/30 border border-yellow-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                  <span className="text-yellow-300 font-medium text-sm">
                                    Note
                                  </span>
                                </div>
                                <div className="text-yellow-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'query' && (
                              <div className="bg-purple-900/30 border border-purple-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                  <span className="text-purple-300 font-medium text-sm">
                                    Query
                                  </span>
                                </div>
                                <div className="text-purple-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'response' && (
                              <div className="bg-indigo-900/30 border border-indigo-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                                  <span className="text-indigo-300 font-medium text-sm">
                                    Response
                                  </span>
                                </div>
                                <div className="text-indigo-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'example' && (
                              <div className="bg-teal-900/30 border border-teal-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-teal-400 rounded-full"></div>
                                  <span className="text-teal-300 font-medium text-sm">
                                    Example
                                  </span>
                                </div>
                                <div className="text-teal-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                            {specialMsg.type === 'error' && (
                              <div className="bg-red-900/30 border border-red-700/50 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                                  <span className="text-red-300 font-medium text-sm">
                                    Error
                                  </span>
                                </div>
                                <div className="text-red-100 text-sm leading-relaxed whitespace-pre-wrap">
                                  {specialMsg.content}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}

                        {hasFilterableTags && isStreaming && (
                          <div className="bg-[#1a2027] rounded-2xl p-4 mt-4">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-8 h-8 rounded-full border border-gray-600 flex items-center justify-center">
                                <Code className="w-4 h-4 text-gray-400" />
                              </div>
                              <span className="text-gray-200">
                                Generating Code...
                              </span>
                              <Loader className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>

                {message.experimental_attachments && (
                  <div className="flex items-center gap-2 mt-6">
                    {message.experimental_attachments
                      ?.filter((attachment) =>
                        attachment.contentType?.startsWith('image/'),
                      )
                      .map((attachment, index) => (
                        <img
                          key={`${message.id}-${index}`}
                          src={attachment.url}
                          alt={attachment.name}
                          className="w-32 h-32 rounded-md"
                        />
                      ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />

        {/* Scroll to bottom button */}
        {showScrollButton && (
          <button
            onClick={scrollToBottom}
            className="absolute border border-black-300 animate-bounce bottom-3 right-[50%] bg-black-300 hover:bg-black-400 text-white rounded-full p-2 shadow-md transition-all duration-200 z-10"
            aria-label="Scroll to bottom"
          >
            <ArrowDown className="h-5 w-5 text-gray-400" />
          </button>
        )}
      </ScrollArea>

      {/* Supabase Connection Card */}
      {showSupabaseCard && <SupabaseConnectionCard />}

      {/* Error Resolution Card */}
      {showErrorCard && buildError && (
        <ErrorResolutionCard
          error={buildError}
          onResolveError={handleResolveError}
        />
      )}

      <form className="relative">
        <div
          className={cn(
            'rounded-2xl border border-black-300 bg-black-100 px-3 transition-all duration-300',
            isFocused
              ? 'ring-1 ring-white/20 transition-all duration-300'
              : 'ring-0',
          )}
        >
          {/* File previews */}
          {filePreviews.length > 0 && (
            <div className="flex flex-wrap gap-2 pt-3 px-2">
              {filePreviews.map((preview, index) => (
                <div key={index} className="relative group">
                  <img
                    src={preview.url}
                    alt={`Preview ${index}`}
                    className="w-20 h-20 object-cover rounded-md"
                  />
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="absolute -top-2 -right-2 bg-black-300 hover:bg-black-400 rounded-full p-1"
                    aria-label="Remove file"
                  >
                    <X className="h-4 w-4 text-white" />
                  </button>
                </div>
              ))}
            </div>
          )}

          <AutoResizeTextarea
            value={input}
            placeholder="Ask Full Stack Fox..."
            onChange={handleInputChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            rows={1}
            className="resize-none focus-visible:ring-0 scrollbar-hide border-none overflow-auto w-full flex-1 bg-transparent px-3 py-4 pb-0 text-base text-white outline-none ring-0 placeholder:text-gray-500"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (input.trim()) {
                  handleSubmit();
                }
              }
            }}
          />

          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2 text-sm text-[#F4F4F5] transition-colors"
              >
                <ImageIcon className="h-5 w-5" />
                <span>Attach</span>
              </button>

              <input
                hidden
                type="file"
                onChange={handleFileChange}
                multiple
                max={1}
                ref={fileInputRef}
                accept="image/*"
              />
            </div>

            <button
              type="button"
              onClick={isStreaming ? handleStop : handleSubmit}
              className={cn(
                'flex h-8 w-8 items-center justify-center rounded-full bg-muted-tertiary text-white hover:bg-muted-tertiary disabled:opacity-50 disabled:cursor-not-allowed transition-colors',
                (input.length > 0 || isStreaming) && 'bg-white hover:bg-white',
              )}
            >
              {isStreaming ? (
                <Square className="h-3 w-3 text-black-100 bg-black-100" />
              ) : (
                <ArrowUp className="h-5 w-5 text-black-100" />
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};
