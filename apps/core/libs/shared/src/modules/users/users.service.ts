import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RecoveryCode, User } from '@prisma/client';
import {
  compareRecoveryCode,
  hashRecoveryCode,
} from '@shared-library/common/utils/bycrypt';
import { generateCryptoSecret } from '@shared-library/common/utils/crypto';
import { EVENTS } from '@shared-library/common/utils/events';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { IUserCreate } from '../auth/interfaces/user-create.interface';
import {
  USER_ATTRIBUTES,
  UserAttributes,
} from './constants/user-profile.constant';
import { UserCreatedEvent } from './events/user.event';

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2,
  ) {}
  /**
   *
   * @param user
   * @param flyAppName
   * @returns
   */
  async create(user: IUserCreate, flyAppName?: string | null): Promise<User> {
    const verificationSecret = generateCryptoSecret(32);
    const newUser = await this.prisma.user.create({
      data: {
        email: user.email,
        password_hash: user.password,
        is_active: true,
        verification_secret: verificationSecret,
        fly_app_name: flyAppName,
      },
    });

    const profileData: UserAttributes = {};
    profileData[USER_ATTRIBUTES.NAME] = user.name;
    await this.upsertUserProfile(newUser, profileData);

    const { password_hash, ...safeUser } = newUser;

    this.eventEmitter.emit(
      EVENTS.USER_CREATED,
      new UserCreatedEvent(safeUser.id, safeUser.email),
    );
    return safeUser as User;
  }

  /**
   *
   * @param email
   * @returns
   */
  async getUserByEmail(email: string): Promise<User | null> {
    const user = await this.prisma.user.findFirst({
      where: {
        email: email,
      },
    });
    return user;
  }

  async find(id: number): Promise<User | null> {
    const user = await this.prisma.user.findFirst({
      where: {
        id: id,
      },
    });
    return user;
  }

  /**
   *
   * @param user
   * @returns
   */
  async update(user: User): Promise<User> {
    const updatedUser = await this.prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        ...user,
      },
    });
    return updatedUser as User;
  }

  async deleteRecoveryCodes(user: User): Promise<void> {
    await this.prisma.recoveryCode.deleteMany({
      where: {
        user_id: user.id,
      },
    });
  }

  async saveRecoveryCodes(user: User, codes: string[]): Promise<void> {
    const recoveryCodes = await Promise.all(
      codes.map(async (code) => ({
        user_id: user.id,
        code: await hashRecoveryCode(code),
      })),
    );

    await this.prisma.recoveryCode.createMany({
      data: recoveryCodes,
    });
  }

  async deleteRecoveryCode(recoveryCodeObj: RecoveryCode) {
    await this.prisma.recoveryCode.delete({
      where: {
        id: recoveryCodeObj.id,
      },
    });
  }

  async validateRecoveryCode(
    user: User,
    recoveryCode: string,
  ): Promise<RecoveryCode> {
    const recoveryCodes = await this.prisma.recoveryCode.findMany({
      where: {
        user_id: user.id,
      },
    });

    for (const code of recoveryCodes) {
      if (await compareRecoveryCode(recoveryCode, code.code)) {
        //match found
        return code;
      }
    }
    throw new UnprocessableEntityException('Invalid recovery code');
  }

  async createOrFindUser(userInfo: any) {
    return this.prisma.user.upsert({
      where: { email: userInfo.email },
      update: {},
      create: {
        email: userInfo.email,
        is_email_verified: true,
        email_verified_at: new Date(),
        is_active: true,
      },
    });
  }

  async upsertUserProfile(user: User, data: UserAttributes) {
    const userId = user.id;

    for (const [attributeKey, value] of Object.entries(data)) {
      if (!Object.values(USER_ATTRIBUTES).includes(attributeKey)) {
        throw new UnprocessableEntityException(
          `Invalid attribute key: ${attributeKey}`,
        );
      }

      await this.prisma.userProfile.upsert({
        where: {
          user_id_attribute: {
            user_id: userId,
            attribute: attributeKey,
          },
        },
        update: {
          value,
          updated_at: new Date(),
        },
        create: {
          user_id: userId,
          attribute: attributeKey,
          value,
        },
      });
    }
  }

  async getUserProfile(user: User): Promise<any> {
    return await this.prisma.userProfile.findMany({
      where: {
        user_id: user.id,
      },
    });
  }
}
