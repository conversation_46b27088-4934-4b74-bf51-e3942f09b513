import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { USER_ATTRIBUTES } from '@shared-library/modules/users/constants/user-profile.constant';
import { AuthorizationCode } from 'simple-oauth2';
import { AuthService } from '../auth.service';
import { IGoogleUser } from '../interfaces/google-user.interface';
import { LoginResponse } from '../interfaces/login-response';

@Injectable()
export class GoogleAuthService extends AuthService {
  private readonly client;

  constructor(authService: AuthService) {
    super(
      authService['usersService'],
      authService['tokenService'],
      authService['prisma'],
    );
    this.client = new AuthorizationCode({
      client: {
        id: process.env.GOOGLE_CLIENT_ID,
        secret: process.env.GOOGLE_CLIENT_SECRET,
      },
      auth: {
        authorizeHost: 'https://accounts.google.com',
        authorizePath: '/o/oauth2/auth',

        tokenHost: 'https://oauth2.googleapis.com',
        tokenPath: '/token',
      },
    });
  }

  /**
   * Redirects the user to Google's OAuth2 consent screen.
   */
  getAuthorizationUrl(): string {
    try {
      const url = this.client.authorizeURL({
        redirect_uri: process.env.GOOGLE_REDIRECT_URI,
        scope: ['profile', 'email'],
        state: process.env.OAUTH_GOOGLE_STATE,
      });
      return url;
    } catch (error) {
      console.error('Error generating Google OAuth URL:', error);
      throw new InternalServerErrorException(
        'Failed to generate Google OAuth URL. please try again later.',
      );
    }
  }

  /**
   * Handles the callback from Google and saves OAuth details in the database.
   */
  async handleCallback(code: string): Promise<LoginResponse> {
    try {
      const tokenParams = {
        code,
        redirect_uri: process.env.GOOGLE_REDIRECT_URI,
      };

      const googleAccess = await this.client.getToken(tokenParams);

      const userInfo: IGoogleUser = await this.getGoogleUserInfo(
        googleAccess.token.access_token,
      );

      let user = await this.createOrFindUser(userInfo);

      // Create fly app for new OAuth users if they don't have one
      if (!user.fly_app_name && this.flyPreviewManager) {
        try {
          const flyAppName = require('uuid').v4(); // Use UUID for unique app names
          await this.flyPreviewManager.createApp(flyAppName);

          // Update user with fly app name
          user = await this.usersService.update({
            ...user,
            fly_app_name: flyAppName,
          });

          // Fly app created successfully for OAuth user
        } catch (error) {
          console.error(
            `Failed to create fly app for OAuth user ${user.email}:`,
            error,
          );
          // Continue even if fly app creation fails
        }
      }

      await this.usersService.upsertUserProfile(user, {
        [USER_ATTRIBUTES.NAME]: userInfo.name,
        [USER_ATTRIBUTES.PROFILE_PICTURE]: userInfo.picture,
      });

      await this.prisma.oauthProvider.upsert({
        where: { identifier: userInfo.sub },
        update: {
          user_id: user.id,
          expires_at: new Date(googleAccess.token.expires_at),
        },
        create: {
          name: 'google',
          identifier: userInfo.sub,
          user_id: user.id,
          expires_at: new Date(googleAccess.token.expires_at),
        },
      });

      const { accessToken, refreshToken } =
        await this.tokenService.generateTokens(user);

      user.last_login = new Date();
      await this.usersService.update(user);

      const workspaces = await this.prisma.workspace.findFirst({
        where: {
          owner_id: user.id,
        },
      });
      const firstWorkspace = await this.prisma.onboardingProgress.findUnique({
        where: { workspaceId: workspaces.id },
      });

      return <LoginResponse>{
        message: 'User logged in successfully',
        data: {
          accessToken,
          refreshToken,
          furtherAction: null,
          workspace: firstWorkspace,
        },
      };
    } catch (error) {
      console.error('Error handling Google OAuth callback:', error);
      throw new Error('OAuth callback failed.');
    }
  }

  /**
   * Fetches user information from Google using the access token.
   */
  private async getGoogleUserInfo(accessToken: string): Promise<IGoogleUser> {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v3/userinfo',
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      },
    );
    if (!response.ok) {
      throw new Error('Failed to fetch user info from Google');
    }

    return response.json();
  }
}
