import { Injectable, Logger } from '@nestjs/common';
import { Daytona } from '@daytonaio/sdk';

export interface DaytonaSandboxResult {
  sandboxId: string;
  previewUrl: string;
  previewToken: string;
  filesCount?: number;
  provider: 'daytona';
  message: string;
}

export interface DaytonaUploadResult {
  success: boolean;
  filesCount: number;
  message: string;
}

export interface DaytonaHealthCheckResult {
  status: 'healthy' | 'unhealthy';
  message: string;
  timestamp: string;
}

@Injectable()
export class DaytonaService {
  private readonly logger = new Logger(DaytonaService.name);
  private daytona: Daytona;

  constructor() {
    this.daytona = new Daytona();
    this.logger.log('DaytonaService initialized');
  }

  /**
   * Create a new Daytona sandbox
   */
  async createSandbox(): Promise<DaytonaSandboxResult> {
    this.logger.log('Creating Daytona sandbox...');

    try {
      const sandbox = await this.daytona.create({
        snapshot: 'fullstackfox',
        autoStopInterval: 2, // 2 minutes
        autoDeleteInterval: 5, // 5 minutes
        public: true,
      });

      this.logger.log(`Sandbox created: ${sandbox.id}`);

      // Get preview link
      const previewInfo = await sandbox.getPreviewLink(3000);
      this.logger.log(`Preview link generated: ${previewInfo.url}`);

      return {
        sandboxId: sandbox.id,
        previewUrl: previewInfo.url,
        previewToken: previewInfo.token,
        provider: 'daytona',
        message: 'Daytona sandbox created successfully',
      };
    } catch (error) {
      this.logger.error('Failed to create Daytona sandbox:', error);
      throw new Error(`Sandbox creation failed: ${error.message}`);
    }
  }

  /**
   * Destroy a Daytona sandbox
   */
  async destroySandbox(sandboxId: string): Promise<void> {
    this.logger.log(`Destroying sandbox: ${sandboxId}`);

    try {
      const sandbox = this.daytona.find_one(sandboxId);
      if (sandbox) {
        await sandbox.delete();
        this.logger.log(`Sandbox ${sandboxId} destroyed successfully`);
      } else {
        this.logger.warn(`Sandbox ${sandboxId} not found`);
      }
    } catch (error) {
      this.logger.error(`Failed to destroy sandbox ${sandboxId}:`, error);
      // Don't throw error - cleanup failures shouldn't break the flow
    }
  }

  /**
   * Upload files to an existing sandbox
   */
  async uploadFiles(
    sandboxId: string,
    files: Record<string, string>,
  ): Promise<DaytonaUploadResult> {
    this.logger.log(`Uploading files to sandbox: ${sandboxId}`);

    try {
      const sandbox = this.daytona.find_one(sandboxId);
      if (!sandbox) {
        throw new Error(`Sandbox ${sandboxId} not found`);
      }

      // Convert Record<string, string> to the required format
      const uploadFiles = Object.entries(files).map(([destination, content]) => {
        const contentString = typeof content === 'string' ? content : String(content);
        return {
          source: Buffer.from(contentString, 'utf8'),
          destination: destination,
        };
      });

      await sandbox.fs.uploadFiles(uploadFiles);
      this.logger.log(`Uploaded ${uploadFiles.length} files to sandbox`);

      return {
        success: true,
        filesCount: uploadFiles.length,
        message: 'Files uploaded successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to upload files to sandbox ${sandboxId}:`, error);
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  /**
   * Health check for a preview URL
   */
  async healthCheck(previewUrl: string): Promise<DaytonaHealthCheckResult> {
    this.logger.log(`Health check for: ${previewUrl}`);

    try {
      const response = await fetch(`${previewUrl}/api/health-check`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });

      if (response.ok) {
        return {
          status: 'healthy',
          message: 'Preview is healthy and ready',
          timestamp: new Date().toISOString(),
        };
      } else {
        return {
          status: 'unhealthy',
          message: `Health check failed with status: ${response.status}`,
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Health check failed: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Health check with polling and timeout
   */
  async healthCheckWithTimeout(
    previewUrl: string,
    timeoutMs: number = 15000,
  ): Promise<void> {
    const startTime = Date.now();
    const pollInterval = 500; // 500ms

    while (Date.now() - startTime < timeoutMs) {
      const result = await this.healthCheck(previewUrl);
      
      if (result.status === 'healthy') {
        this.logger.log(`Health check passed for ${previewUrl}`);
        return;
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    throw new Error(`Health check timeout after ${timeoutMs}ms`);
  }
}
