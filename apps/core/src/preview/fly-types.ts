// **************** Fly.io App Types ****************
export interface FlyApp {
  id: string;
  name: string;
  status: string;
  organization: {
    name: string;
    slug: string;
  };
}

export interface FlyCreateAppResponse {
  id: string;
  created_at: number;
}

export interface FlyCreateAppRequest {
  app_name: string;
  org_slug: string;
  network?: string;
  enable_subdomains?: boolean;
}

export interface FlyAppsListResponse {
  apps: Array<{
    id: string;
    name: string;
    network: string;
    machine_count: number;
  }>;
  total_apps: number;
}

// **************** Fly.io Machine Types ****************
export interface FlyMachine {
  id: string;
  name: string;
  state: FlyMachineState;
  config: FlyMachineConfig;
  events: FlyMachineEvent[];
  region: string;
  image_ref: {
    tag: string;
    digest: string;
    registry: string;
    repository: string;
  };
  created_at: string;
  private_ip: string;
  updated_at: string;
  instance_id: string;
}

export interface FlyMachineConfig {
  init?: {
    exec: string[];
  };
  guest: {
    cpus: number;
    cpu_kind: string;
    memory_mb: number;
  };
  image: string;
  restart: {
    policy: string;
  };
  auto_destroy: boolean;
  services?: Array<{
    ports: Array<{
      port: number;
      handlers: string[];
    }>;
    protocol: string;
    internal_port: number;
  }>;
  env?: Record<string, string>;
  metadata?: Record<string, string>;
  mounts?: Array<{
    source: string;
    destination: string;
    type: string;
  }>;
  processes?: Array<{
    name: string;
    entrypoint: string[];
    cmd: string[];
    env: Record<string, string>;
  }>;
}

export interface FlyMachineEvent {
  type: string;
  source: string;
  status: string;
  timestamp: number;
}

export interface FlyCreateMachineRequest {
  name: string;
  config: FlyMachineConfig;
  region: string;
  lease_ttl?: number;
  skip_launch?: boolean;
  skip_service_registration?: boolean;
}

export interface FlyUpdateMachineRequest {
  name?: string;
  config?: FlyMachineConfig;
  region?: string;
  lease_ttl?: number;
  skip_launch?: boolean;
  current_version?: string;
  skip_service_registration?: boolean;
}

export interface FlyWaitResponse {
  ok: boolean;
}

export interface FlyMachineStartResponse {
  migrated: boolean;
  new_host: string;
  previous_state: string;
}

export interface FlyMachineStopResponse {
  ok: boolean;
}

export interface FlyMachineSuspendResponse {
  ok: boolean;
}

export interface FlyMachineDeleteResponse {
  ok: boolean;
}

// **************** Machine States ****************
export type FlyMachineState =
  | 'created'
  | 'starting'
  | 'started'
  | 'stopping'
  | 'stopped'
  | 'replacing'
  | 'destroying'
  | 'destroyed'
  | 'suspended';

// **************** Machine Lease Types ****************
export interface FlyMachineLease {
  data: {
    nonce: string;
    owner: string;
    version: string;
    expires_at: number;
    description: string;
  };
  status: string;
}

export interface FlyMachineLeaseRequest {
  ttl: number;
  description?: string;
}

// **************** Machine Metadata Types ****************
export interface FlyMachineMetadata {
  [key: string]: string;
}

export interface FlyMachineMetadataUpdateRequest {
  key: string;
  value: string;
}

// **************** Wait Parameters ****************
export interface FlyWaitParams {
  state?: FlyMachineState;
  timeout?: number;
  instance_id?: string;
}

// **************** Machine List Parameters ****************
export interface FlyMachineListParams {
  region?: string;
  include_deleted?: boolean;
  'metadata.{key}'?: string;
}

// **************** Machine Stop Parameters ****************
export interface FlyMachineStopParams {
  signal?: string;
  timeout?: string;
}

// **************** App Delete Parameters ****************
export interface FlyAppDeleteParams {
  force?: boolean;
}

// **************** Error Response Types ****************
export interface FlyErrorResponse {
  error: string;
  message?: string;
  details?: any;
}

// **************** API Response Wrapper ****************
export interface FlyApiResponse<T> {
  data?: T;
  error?: FlyErrorResponse;
  status: number;
}

// **************** Preview Manager Response Types ****************
export interface FlyPreviewResult {
  machineId: string;
  appId: string;
  appUrl: string;
  appName: string;
}

export interface FlyPreviewCreateResponse {
  machineId: string;
  appId: string;
  previewUrl: string;
  wsUrl: string;
  filesCount: number;
  provider: 'fly';
}

// **************** Health Check Types ****************
export interface FlyHealthCheckResponse {
  status: 'ok' | 'error';
  message?: string;
  timestamp: string;
}

// **************** Code Injection Types ****************
export interface FlyCodeInjectionRequest {
  files: Record<string, string>;
}

export interface FlyCodeInjectionResponse {
  message: string;
  filesProcessed: number;
  timestamp: string;
}

export interface FlyAllocateIpResponse {
  data: {
    allocateIpAddress: {
      ipAddress: {
        address: string;
      };
    };
  };
  errors?: Array<{
    message: string;
    locations?: Array<{
      line: number;
      column: number;
    }>;
    path?: string[];
  }>;
}

// Runtime error monitoring types
export interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

export interface FlyLogsResponse {
  data: {
    app: {
      recentLogs: LogEntry[];
    };
  };
  errors?: Array<{
    message: string;
    locations?: Array<{
      line: number;
      column: number;
    }>;
    path?: string[];
  }>;
}

export interface RuntimeError {
  message: string;
  level: string;
  timestamp: string;
  region: string;
  instanceId: string;
}

// **************** Machine Configuration Presets ****************
export const FLY_MACHINE_PRESETS = {
  SMALL: {
    cpus: 1,
    cpu_kind: 'shared',
    memory_mb: 1024,
  },
  MEDIUM: {
    cpus: 2,
    cpu_kind: 'shared',
    memory_mb: 1024,
  },
  LARGE: {
    cpus: 4,
    cpu_kind: 'shared',
    memory_mb: 2048,
  },
} as const;

// **************** Service Configuration ****************
export const FLY_SERVICE_CONFIG = {
  HTTP_SERVICE: {
    ports: [
      {
        port: 80,
        handlers: ['http'],
      },
      {
        port: 443,
        handlers: ['tls', 'http'],
      },
    ],
    protocol: 'tcp',
    internal_port: 3000,
  },
} as const;

// **************** Region Constants ****************
export const FLY_REGIONS = {
  SINGAPORE: 'sin',
  LOS_ANGELES: 'lax',
  FRANKFURT: 'fra',
  SYDNEY: 'syd',
  TOKYO: 'nrt',
  LONDON: 'lhr',
  NEW_YORK: 'ewr',
} as const;

export type FlyRegion = (typeof FLY_REGIONS)[keyof typeof FLY_REGIONS];
