import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { FlyAxiosInstance } from './fly-axios-instance';
import {
  FlyMachine,
  FlyCreateAppResponse,
  FlyCreateMachineRequest,
  FlyWaitResponse,
  FlyPreviewResult,
  FlyCreateAppRequest,
  FlyAppDeleteParams,
  FLY_MACHINE_PRESETS,
  FLY_REGIONS,
  FlyAllocateIpResponse,
  FlyLogsResponse,
  RuntimeError,
} from './fly-types';
import { LogEntry, MachineLogsResult } from './types';
import {
  TWO_MINUTES_MS,
  HEALTH_CHECK_INTERVAL_MS,
  APP_PORT,
  INJECT_CODE_PATH,
  HEALTH_CHECK_PATH,
} from './constants';

export class FlyPreviewManager {
  private flyAxios: FlyAxiosInstance;
  private dockerImage: string;
  private orgSlug: string;

  constructor(apiKey: string, dockerImage: string, orgSlug: string) {
    this.flyAxios = new FlyAxiosInstance(apiKey);
    this.dockerImage = dockerImage;
    this.orgSlug = orgSlug;
  }

  async createApp(appName: string): Promise<FlyCreateAppResponse> {
    const payload: FlyCreateAppRequest = {
      app_name: appName,
      org_slug: this.orgSlug,
    };

    const response = await this.flyAxios.api.post('/v1/apps', payload);
    return response.data;
  }

  async allocateSharedIPv4(appName: string): Promise<string> {
    try {
      console.log(`Allocating shared IPv4 for app: ${appName}`);

      const query = `
        mutation ($input: AllocateIPAddressInput!) {
          allocateIpAddress(input: $input) {
            ipAddress {
              address
            }
          }
        }
      `;

      const variables = {
        input: {
          appId: appName,
          // type: 'shared_v4',
          type: 'private_v6',
        },
      };

      const response = await this.flyAxios.graphql.post('', {
        query,
        variables,
      });

      if (response.data.errors?.length) {
        throw new Error(
          `GraphQL error: ${JSON.stringify(response.data.errors)}`,
        );
      }

      console.log(`✅ Shared IPv4 allocated`);
      return 'success';
    } catch (error) {
      console.error('❌ IPv4 allocation failed:', error);
      throw error;
    }
  }

  async createPreviewAppFromScratch(): Promise<FlyPreviewResult> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const appName = uuidv4(); // Use UUID for unique app names
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = await this.createApp(appName);

    // Step 2: Create Machine inside the App
    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: FLY_MACHINE_PRESETS.SMALL,
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [
          {
            ports: [
              {
                port: 80,
                handlers: ['http'],
              },
              {
                port: 443,
                handlers: ['tls', 'http'],
              },
            ],
            protocol: 'tcp',
            internal_port: APP_PORT,
          },
        ],
        env: {
          NODE_ENV: 'development',
          PORT: APP_PORT.toString(),
        },
        metadata: {
          'preview-app': 'true',
          'created-at': new Date().toISOString(),
          timeout: TWO_MINUTES_MS.toString(),
        },
      },
      region: FLY_REGIONS.SINGAPORE,
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      const response = await this.flyAxios.api.post(
        `/v1/apps/${appName}/machines`,
        machineConfig,
      );
      const machine = response.data;

      const appUrl = `https://${appName}.fly.dev`;

      // Step 3: Allocate shared IPv4 address for public accessibility
      await this.allocateSharedIPv4(appName);

      // Step 4: Perform comprehensive health check (machine + application)
      try {
        await this.performFullHealthCheck(appName, machine.id, appUrl);
        console.log(
          `✅ Comprehensive health check passed for app ${appName} (${appUrl})`,
        );
      } catch (error) {
        console.warn(
          `Comprehensive health check failed for app ${appName} (${appUrl}). App may not be fully ready: ${error.message}`,
        );
        // Continue anyway - the app might still work
      }

      // Schedule auto-destruction after 2 minutes
      setTimeout(
        () => {
          this.destroyApp(appName);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || TWO_MINUTES_MS.toString()),
      );

      return {
        machineId: machine.id,
        appId: app.id,
        appUrl,
        appName,
      };
    } catch (error) {
      console.error('Machine creation failed:', error);

      // Clean up the app if machine creation fails
      try {
        await this.destroyApp(appName);
        console.log(`App ${appName} deleted after machine creation failure`);
      } catch (cleanupError) {
        console.error(
          `Failed to clean up app ${appName}:`,
          cleanupError.message || cleanupError,
        );
        // Don't re-throw cleanup errors - the original error is more important
      }

      throw error;
    }
  }

  async getMachine(appName: string, machineId: string): Promise<FlyMachine> {
    const response = await this.flyAxios.api.get(
      `/v1/apps/${appName}/machines/${machineId}`,
    );
    return response.data;
  }

  /**
   * MACHINE INFRASTRUCTURE HEALTH CHECK
   * Waits for the Fly machine to reach 'started' state (infrastructure level)
   * This checks that the machine itself is running, not the application inside it
   */
  async checkMachineStatus(appName: string, machineId: string): Promise<void> {
    console.log(
      `🔧 Checking machine infrastructure status for ${machineId}...`,
    );

    try {
      // Use server-side long polling with 30 second timeout
      // The server will hold the connection open until the machine is ready or timeout
      await this.flyAxios.api.get(
        `/v1/apps/${appName}/machines/${machineId}/wait?state=started&timeout=30`,
      );

      console.log(
        `✅ Machine ${machineId} infrastructure is ready and started`,
      );
      return;
    } catch (error) {
      // Handle specific response codes from the /wait endpoint
      if (error.message?.includes('408')) {
        // Request timeout - machine didn't reach 'started' state within 30s
        console.error(
          `❌ Machine ${machineId} failed to start within 30 seconds`,
        );
        throw new Error('Machine failed to start within timeout period');
      } else if (error.message?.includes('400')) {
        // Bad request - invalid state or machine ID
        console.error(`❌ Invalid wait request for machine ${machineId}`);
        throw new Error('Invalid machine ID or state parameter');
      } else {
        // Other errors (network, auth, etc.)
        console.error(
          `❌ Unexpected error checking machine ${machineId} status:`,
          error,
        );
        throw new Error(`Failed to check machine status: ${error.message}`);
      }
    }
  }

  /**
   * APPLICATION HEALTH CHECK
   * Checks if the application inside the machine is responding to HTTP requests
   * This verifies that the app is fully loaded and ready to serve requests
   */
  async checkApplicationHealth(appUrl: string): Promise<void> {
    const healthUrl = `${appUrl}${HEALTH_CHECK_PATH}`;
    let attempts = 0;
    const maxAttempts = 60; // 30 seconds (60 * 500ms) - increased for slower app startup

    console.log(`🏥 Starting application health check for ${healthUrl}`);

    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(healthUrl, {
          timeout: 5000, // 5 second timeout
        });

        if (response.status === 200) {
          console.log('✅ Application health check passed');
          return;
        } else {
          console.log(
            `Application health check attempt ${attempts + 1}: received status ${response.status}`,
          );
        }
      } catch (error) {
        // Handle different types of errors
        let errorMessage = 'Unknown error';
        if (axios.isAxiosError(error)) {
          if (error.code === 'ECONNABORTED') {
            errorMessage = 'Request timeout';
          } else {
            errorMessage = error.message;
          }
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }

        console.log(
          `Application health check attempt ${attempts + 1} failed: ${errorMessage}`,
        );
      }

      await new Promise((resolve) =>
        setTimeout(resolve, HEALTH_CHECK_INTERVAL_MS),
      );
      attempts++;
    }

    throw new Error('Application health check failed within timeout');
  }

  /**
   * COMPREHENSIVE HEALTH CHECK
   * Performs both machine infrastructure and application health checks in sequence
   * Use this for complete verification when creating new machines
   */
  async performFullHealthCheck(
    appName: string,
    machineId: string,
    appUrl: string,
  ): Promise<void> {
    console.log(`🔍 Starting comprehensive health check for ${appName}...`);

    // 1. First check machine infrastructure (fast - usually completes immediately)
    await this.checkMachineStatus(appName, machineId);

    // 2. Then check application health (slower - waits for app to respond)
    await this.checkApplicationHealth(appUrl);

    console.log(`✅ Comprehensive health check completed for ${appName}`);
  }

  async injectCode(
    appUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    try {
      const injectionUrl = `${appUrl}${INJECT_CODE_PATH}`;

      console.log(`Attempting code injection to: ${injectionUrl}`);

      const response = await axios.post(
        injectionUrl,
        { files },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        },
      );

      console.log('Code injection successful:', response.data);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Code injection failed:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        });
      } else {
        console.error('Code injection failed:', error);
      }
    }
  }

  // =================== NEW MACHINE MANAGEMENT METHODS ===================

  /**
   * Suspend a machine to save resources
   */
  async suspendMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.flyAxios.api.post(
        `/v1/apps/${appName}/machines/${machineId}/suspend`,
      );
    } catch (error) {
      console.error(`❌ Failed to suspend machine ${machineId}:`, error);
      throw error;
    }
  }

  /**
   * Start a suspended machine
   */
  async startMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.flyAxios.api.post(
        `/v1/apps/${appName}/machines/${machineId}/start`,
      );
    } catch (error) {
      console.error(`❌ Failed to start machine ${machineId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's machine for an app (single machine approach)
   */
  async getUserMachine(appName: string): Promise<FlyMachine | null> {
    try {
      const response = await this.flyAxios.api.get(
        `/v1/apps/${appName}/machines`,
      );
      const machines = response.data;
      return machines.length > 0 ? machines[0] : null;
    } catch (error) {
      console.error(`❌ Failed to get user machine for app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Create a machine in an existing app (for reusing persistent apps)
   */
  async createMachineInExistingApp(appName: string): Promise<FlyMachine> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: FLY_MACHINE_PRESETS.SMALL,
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [
          {
            ports: [
              {
                port: 80,
                handlers: ['http'],
              },
              {
                port: 443,
                handlers: ['tls', 'http'],
              },
            ],
            protocol: 'tcp',
            internal_port: APP_PORT,
          },
        ],
        env: {
          NODE_ENV: 'development',
          PORT: APP_PORT.toString(),
        },
        metadata: {
          'preview-machine': 'true',
          'created-at': new Date().toISOString(),
        },
      },
      region: FLY_REGIONS.SINGAPORE,
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      const response = await this.flyAxios.api.post(
        `/v1/apps/${appName}/machines`,
        machineConfig,
      );
      const machine = response.data;

      // Allocate shared IPv4 address for public accessibility
      await this.allocateSharedIPv4(appName);

      // Wait for the machine infrastructure to be ready
      await this.checkMachineStatus(appName, machine.id);
      return machine;
    } catch (error) {
      console.error(`❌ Failed to create machine in app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Delete user's machine in an app (single machine approach)
   */
  async deleteUserMachine(appName: string): Promise<void> {
    try {
      const machine = await this.getUserMachine(appName);

      if (!machine) {
        return; // No machine to delete
      }

      await this.flyAxios.api.delete(
        `/v1/apps/${appName}/machines/${machine.id}`,
      );
    } catch (error) {
      console.error(`❌ Failed to delete machine from app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Monitor runtime errors in app logs
   */
  async monitorRuntimeErrors(appName: string): Promise<RuntimeError[]> {
    try {
      console.log(`Monitoring runtime errors for app: ${appName}`);

      // Wait for application to process injected code
      await new Promise((resolve) => setTimeout(resolve, 10000));

      const query = `
        query GetAppLogs($appName: String!, $limit: Int!, $range: Int!) {
          app(name: $appName) {
            allocations {
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      `;

      const variables = {
        appName,
        limit: 30,
        range: 60,
      };

      const response = await this.flyAxios.graphql.post('', {
        query,
        variables,
      });

      if (response.data.errors?.length) {
        throw new Error(
          `GraphQL error: ${JSON.stringify(response.data.errors)}`,
        );
      }

      // Flatten logs from all allocations
      const allLogs = response.data.data.app.allocations.flatMap(
        (allocation) => allocation.recentLogs,
      );

      // For testing: return the structured logs as RuntimeError objects
      if (allLogs && allLogs.length > 0) {
        return allLogs.map((log) => ({
          message: log.message,
          level: log.level,
          timestamp: log.timestamp,
          region: log.region,
          instanceId: log.instanceId,
        }));
      }

      return [];
    } catch (error) {
      console.error('❌ Failed to monitor runtime errors:', error);
      return [];
    }
  }

  async destroyApp(appName: string): Promise<void> {
    try {
      const deleteParams: FlyAppDeleteParams = { force: true };

      // Delete the app - this will automatically delete all associated machines
      await this.flyAxios.api.delete(`/v1/apps/${appName}`, {
        data: deleteParams,
      });
      console.log(
        `App ${appName} deleted successfully (machines auto-deleted)`,
      );
    } catch (error) {
      console.error(`Failed to destroy app ${appName}:`, error);
    }
  }

  async stopMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.flyAxios.api.post(
        `/v1/apps/${appName}/machines/${machineId}/stop`,
        { timeout: '30s' },
      );
      console.log(`Machine ${machineId} stopped successfully`);
    } catch (error) {
      console.error(`Failed to stop machine ${machineId}:`, error);
    }
  }

  async deleteMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.flyAxios.api.delete(
        `/v1/apps/${appName}/machines/${machineId}`,
        { data: { force: true } },
      );
      console.log(`Machine ${machineId} deleted successfully`);
    } catch (error) {
      console.error(`Failed to delete machine ${machineId}:`, error);
    }
  }

  /**
   * Get logs for a specific machine in an app
   *
   * @param appName - The name of the Fly.io app
   * @param machineId - The machine/instance ID (can be full ID or short ID)
   * @param limit - Maximum number of log entries to return (default: 100)
   * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
   * @returns Promise with logs data
   */
  async getMachineLogs(
    appName: string,
    machineId: string,
    limit: number = 100,
    range: number = 3600,
  ): Promise<MachineLogsResult> {
    try {
      console.log(
        `🔍 Getting logs for machine: ${machineId} in app: ${appName}`,
      );

      const query = `
        query GetVMLogs($appName: String!, $limit: Int!, $range: Int!) {
          app(name: $appName) {
            vms {
              nodes {
                id
                idShort
                region
                status
                recentLogs(limit: $limit, range: $range) {
                  id
                  instanceId
                  level
                  message
                  region
                  timestamp
                }
              }
            }
          }
        }
      `;

      const variables = {
        appName,
        limit: limit * 1000,
        range: range * 1000,
      };

      const response = await this.flyAxios.graphql.post('', {
        query,
        variables,
      });

      console.log('log response', response.data.data.app.vms);

      if (response.data.errors?.length) {
        throw new Error(
          `GraphQL error: ${JSON.stringify(response.data.errors)}`,
        );
      }

      const app = response.data.data?.app;
      if (!app) {
        throw new Error(`App '${appName}' not found`);
      }

      // Find the target VM by ID (full or partial match)
      const targetVM = app.vms.nodes.find(
        (vm: any) =>
          vm.id === machineId ||
          vm.idShort === machineId ||
          vm.id.startsWith(machineId) ||
          vm.idShort.startsWith(machineId),
      );

      if (!targetVM) {
        // If no specific VM found, collect all logs and filter by instanceId
        let allLogs: LogEntry[] = [];

        for (const vm of app.vms.nodes) {
          if (vm.recentLogs?.length) {
            const vmLogs = vm.recentLogs
              .filter(
                (log: any) =>
                  log.instanceId === machineId ||
                  log.instanceId.startsWith(machineId),
              )
              .map((log: any) => ({
                id: log.id,
                instanceId: log.instanceId,
                level: log.level,
                message: log.message,
                region: log.region,
                timestamp: log.timestamp,
              }));
            allLogs.push(...vmLogs);
          }
        }

        if (allLogs.length === 0) {
          const availableVMs = app.vms.nodes
            .map((vm: any) => `${vm.idShort}`)
            .join(', ');
          throw new Error(
            `No logs found for machine '${machineId}' in app '${appName}'. Available VMs: ${availableVMs}`,
          );
        }

        // Sort by timestamp (newest first)
        allLogs.sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
        );

        console.log(
          `✅ Found ${allLogs.length} log entries by instanceId filtering`,
        );
        return { logs: allLogs, success: true };
      }

      // Extract logs from the specific VM
      const logs: LogEntry[] = (targetVM.recentLogs || []).map((log: any) => ({
        id: log.id,
        instanceId: log.instanceId,
        level: log.level,
        message: log.message,
        region: log.region,
        timestamp: log.timestamp,
      }));

      // Sort by timestamp (newest first)
      logs.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      );

      console.log(
        `✅ Found ${logs.length} log entries for VM ${targetVM.idShort}`,
      );

      return {
        logs,
        success: true,
      };
    } catch (error: any) {
      console.error('❌ Failed to get machine logs:', error);

      return {
        logs: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
