import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { PreviewService } from './preview.service';
import { FlyPreviewManager } from './fly-preview-manager';
import { PrismaService } from 'src/persistence/prisma/prisma.service';

// Mock the FlyPreviewManager
jest.mock('./fly-preview-manager');

describe('PreviewService', () => {
  let service: PreviewService;
  let mockFlyManager: jest.Mocked<FlyPreviewManager>;
  let mockPrisma: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    // Set required environment variables
    process.env.FLY_API_TOKEN = 'test-api-key';
    process.env.DOCKER_IMAGE = 'test-docker-image';
    process.env.FLY_ORG_SLUG = 'test-org';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PreviewService,
        {
          provide: PrismaService,
          useValue: {
            user: {
              findUnique: jest.fn(),
            },
            userMachine: {
              findUnique: jest.fn(),
              upsert: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<PreviewService>(PreviewService);
    mockPrisma = module.get(PrismaService) as jest.Mocked<PrismaService>;

    // Get the mocked FlyPreviewManager instance
    mockFlyManager = (service as any).flyManager;
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.FLY_API_TOKEN;
    delete process.env.DOCKER_IMAGE;
    delete process.env.FLY_ORG_SLUG;
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const result = await service.healthCheck();

      expect(result).toEqual({
        status: 'ok',
        timestamp: expect.any(String),
      });
    });
  });

  describe('createPreview', () => {
    it('should create preview successfully', async () => {
      const mockFiles = { 'test.js': 'console.log("test");' };
      const mockResult = {
        machineId: 'test-machine-id',
        appId: 'test-app-id',
        appUrl: 'https://test-app.fly.dev',
        appName: 'test-app',
      };

      mockFlyManager.createPreviewAppFromScratch = jest
        .fn()
        .mockResolvedValue(mockResult);
      mockFlyManager.injectCode = jest.fn().mockResolvedValue(undefined);

      const result = await service.createPreviewFromScratch(mockFiles);

      expect(result).toEqual({
        machineId: 'test-machine-id',
        appName: 'test-app',
        previewUrl: 'https://test-app.fly.dev',
        wsUrl: 'wss://test-app.fly.dev/ws',
        filesCount: 1,
        provider: 'fly',
        runtimeErrors: [],
        hasRuntimeErrors: false,
      });

      expect(mockFlyManager.injectCode).toHaveBeenCalledWith(
        'https://test-app.fly.dev',
        mockFiles,
      );
    });
  });
});
