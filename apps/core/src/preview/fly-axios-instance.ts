import axios, { AxiosInstance } from 'axios';

export class FlyAxiosInstance {
  public readonly api: AxiosInstance;
  public readonly graphql: AxiosInstance;

  constructor(apiKey: string) {
    // Main Fly.io API instance
    this.api = axios.create({
      baseURL: 'https://api.machines.dev',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    // GraphQL API instance for log monitoring
    this.graphql = axios.create({
      baseURL: 'https://api.fly.io/graphql',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Response interceptor for better error handling
    const errorInterceptor = (error: any) => {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data || error.message;
        const statusCode = error.response?.status || 'unknown';
        console.error(`Fly.io API error: ${statusCode} - ${errorMessage}`);

        // Handle specific timeout scenarios
        if (
          error.code === 'ECONNABORTED' &&
          error.message.includes('timeout')
        ) {
          throw new Error(`Request timeout: ${error.message}`);
        }

        throw new Error(`Fly.io API error: ${statusCode} - ${errorMessage}`);
      }

      throw error;
    };

    this.api.interceptors.response.use(
      (response) => response,
      errorInterceptor,
    );

    this.graphql.interceptors.response.use(
      (response) => response,
      errorInterceptor,
    );
  }
}
