import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { PreviewController } from './preview.controller';
import { PreviewService } from './preview.service';
import { DaytonaService } from './daytona.service';
import { PersistenceModule } from '../persistence/persistence.module';

@Module({
  imports: [
    PersistenceModule,
    JwtGuardModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [PreviewController],
  providers: [PreviewService, DaytonaService],
  exports: [PreviewService, DaytonaService],
})
export class PreviewModule {}
