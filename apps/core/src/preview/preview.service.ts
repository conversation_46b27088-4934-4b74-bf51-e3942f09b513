import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { DaytonaService } from './daytona.service';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);

  constructor(
    private prisma: PrismaService,
    private daytonaService: DaytonaService,
  ) {
    this.logger.log('PreviewService initialized with Daytona integration');
  }

  async healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'preview',
      provider: 'daytona',
    };
  }

  /**
   * Create a preview using Daytona
   */
  async createPreview(files: Record<string, string>) {
    this.logger.log('Creating preview with Daytona');

    try {
      // Step 1: Create sandbox
      const sandboxResult = await this.daytonaService.createSandbox();

      // Step 2: Upload files
      const uploadResult = await this.daytonaService.uploadFiles(
        sandboxResult.sandboxId,
        files,
      );

      // Step 3: Wait for health check
      await this.daytonaService.healthCheckWithTimeout(
        sandboxResult.previewUrl,
      );

      return {
        ...sandboxResult,
        filesCount: uploadResult.filesCount,
        success: true,
      };
    } catch (error) {
      this.logger.error('Preview creation failed:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  /**
   * Upload files to existing sandbox
   */
  async uploadFilesToSandbox(sandboxId: string, files: Record<string, string>) {
    this.logger.log(`Uploading files to sandbox: ${sandboxId}`);
    return this.daytonaService.uploadFiles(sandboxId, files);
  }

  /**
   * Destroy sandbox
   */
  async destroySandbox(sandboxId: string) {
    this.logger.log(`Destroying sandbox: ${sandboxId}`);
    return this.daytonaService.destroySandbox(sandboxId);
  }

  /**
   * Health check for preview URL
   */
  async checkPreviewHealth(previewUrl: string) {
    return this.daytonaService.healthCheck(previewUrl);
  }
}
