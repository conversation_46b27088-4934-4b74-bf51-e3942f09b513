import { Injectable, Logger } from '@nestjs/common';
import { FlyPreviewManager } from './fly-preview-manager';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import {
  PreviewResult,
  HealthCheckResult,
  ServiceHealthCheck,
  MachineResult,
  UserMachineRecord,
  UserWithFlyApp,
  CleanupResult,
  MachineStatus,
  CreatePreviewForUserParams,
  SuspendMachineParams,
  SetUserMachineParams,
  UpdateMachineStatusParams,
  CreateNewMachineForUserParams,
  HandleExistingMachineParams,
  ReactivateMachineParams,
  ReplaceBrokenMachineParams,
  BuildMachineResponseParams,
  MachineLogsResult,
} from './types';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private flyManager: FlyPreviewManager;

  constructor(private prisma: PrismaService) {
    const apiKey = process.env.FLY_API_TOKEN;
    const dockerImage = process.env.DOCKER_IMAGE;
    const orgSlug = process.env.FLY_ORG_SLUG;

    if (!apiKey) {
      throw new Error('FLY_API_TOKEN environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    if (!orgSlug) {
      throw new Error('FLY_ORG_SLUG environment variable is required');
    }

    this.flyManager = new FlyPreviewManager(apiKey, dockerImage, orgSlug);
    this.logger.log('PreviewService initialized with Fly.io integration');
  }

  // NOTE: UserMachine model exists in schema but needs migration before single machine methods work
  // Run: pnpx prisma migrate dev --name add_user_machine_tracking
  // Then: pnpx prisma generate

  async createPreviewFromScratch(files: Record<string, string>) {
    this.logger.log('Starting preview creation process with Fly.io');

    try {
      // Step 1: Create Fly.io app with machine (2-3 seconds)
      this.logger.log('Creating Fly.io app with machine...');

      const createResult = await this.flyManager.createPreviewAppFromScratch();

      const { machineId, appUrl, appName } = createResult;

      // Step 2: Files are already in the correct format
      this.logger.log(
        `Processing ${Object.keys(files).length} files for injection`,
      );

      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Step 3: Inject code into running machine
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      // Step 4: Monitor for runtime errors (separate call)
      // const runtimeErrors = await this.flyManager.monitorRuntimeErrors(appName);

      // if (runtimeErrors.length > 0) {
      //   this.logger.warn(
      //     `Runtime errors detected: ${runtimeErrors.length} errors`,
      //   );
      // }

      const result: PreviewResult = {
        machineId,
        appName,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
        provider: 'fly' as const,
        // runtimeErrors: runtimeErrors,
        // hasRuntimeErrors: runtimeErrors.length > 0,
        runtimeErrors: [],
        hasRuntimeErrors: false,
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  /**
   * OPTIMIZED: Create preview using user's persistent app
   */
  async createPreviewForUser({
    files,
    userId,
  }: CreatePreviewForUserParams): Promise<PreviewResult> {
    this.logger.log(`Starting optimized preview creation for user ${userId}`);

    try {
      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Get user's fly app name
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { fly_app_name: true },
      });

      if (!user?.fly_app_name) {
        // Fallback to old method if user has no persistent app
        this.logger.log(
          `User ${userId} has no persistent app, falling back to old method`,
        );
        const result = await this.createPreviewFromScratch(files);

        // Update user's profile with the new app name for future optimized previews
        try {
          await this.prisma.user.update({
            where: { id: userId },
            data: { fly_app_name: result.appName },
          });
          this.logger.log(
            `✅ Updated user ${userId} profile with new fly app: ${result.appName}`,
          );
        } catch (updateError) {
          this.logger.error(
            `Failed to update user ${userId} profile with fly app name:`,
            updateError,
          );
          // Don't throw - the preview was created successfully
        }

        return result;
      }

      const appName = user.fly_app_name;
      const appUrl = `https://${appName}.fly.dev`;

      // OPTIMIZATION: Just do a quick application health check (machines should already be ready from background call)
      this.logger.log(
        `Performing application health check for app: ${appName}`,
      );
      try {
        await this.flyManager.checkApplicationHealth(appUrl);
        this.logger.log(`✅ App ${appName} application is healthy and ready`);
      } catch (healthError) {
        this.logger.warn(
          `Application health check failed, but continuing: ${healthError.message}`,
        );
        // Continue anyway - the app might still work for code injection
      }

      // Get machine ID from database (single machine per user)
      const currentMachine = await this.getCurrentUserMachine(userId);
      const machineId = currentMachine?.machineId || 'unknown';

      // Step 2: Inject code into running machine (fast operation)
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      const result: PreviewResult = {
        machineId,
        appName,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
        provider: 'fly' as const,
        runtimeErrors: [],
        hasRuntimeErrors: false,
      };

      this.logger.log(
        `✅ Optimized preview created successfully: ${result.previewUrl}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create optimized preview for user ${userId}:`,
        error,
      );
    }
  }

  async healthCheck(): Promise<ServiceHealthCheck> {
    return {
      status: 'ok',
      service: 'fly-preview',
      timestamp: new Date().toISOString(),
    };
  }

  // =================== NEW MACHINE MANAGEMENT METHODS ===================

  /**
   * Suspend a machine to save resources
   */
  async suspendMachine({
    appName,
    machineId,
  }: SuspendMachineParams): Promise<void> {
    this.logger.log(`Suspending machine ${machineId} in app ${appName}`);
    return this.flyManager.suspendMachine(appName, machineId);
  }

  /**
   * Get logs for a specific machine in an app
   */
  async getMachineLogs(
    appName: string,
    machineId: string,
    limit: number = 100,
    range: number = 3600,
  ): Promise<MachineLogsResult> {
    this.logger.log(`Getting logs for machine ${machineId} in app ${appName}`);
    return this.flyManager.getMachineLogs(appName, machineId, limit, range);
  }

  /**
   * Health check and reactivate machine if needed (single machine approach)
   */
  async healthCheckAndReactivate(appName: string): Promise<HealthCheckResult> {
    this.logger.log(`Health check and reactivation for app ${appName}`);

    try {
      // Get user's machine for the app
      const machine = await this.flyManager.getUserMachine(appName);

      if (!machine) {
        return {
          status: 'no_machine',
          message: 'No machine found in app',
          appName,
          timestamp: new Date().toISOString(),
        };
      }

      // Check if machine is suspended and start it
      if (machine.state === 'suspended') {
        this.logger.log(`Found suspended machine ${machine.id}, starting it`);

        await this.flyManager.startMachine(appName, machine.id);

        // Verify machine infrastructure is ready after starting
        await this.flyManager.checkMachineStatus(appName, machine.id);
      }

      // Perform application health check on the app
      const appUrl = `https://${appName}.fly.dev`;

      try {
        await this.flyManager.checkApplicationHealth(appUrl);
        return {
          status: 'healthy',
          message: 'App is healthy and ready',
          appName,
          appUrl,
          machineRestarted: machine.state === 'suspended',
          timestamp: new Date().toISOString(),
        };
      } catch (healthError) {
        return {
          status: 'unhealthy',
          message: 'App health check failed',
          appName,
          error: healthError.message,
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      this.logger.error(`Health check failed for app ${appName}:`, error);
      throw error;
    }
  }

  /**
   * Cleanup user machine (called on logout) - SINGLE MACHINE APPROACH
   */
  async cleanupUserMachine(userId: number): Promise<CleanupResult> {
    this.logger.log(`Cleaning up machine for user ${userId}`);

    try {
      const machine = await this.getCurrentUserMachine(userId);
      if (machine) {
        try {
          await this.flyManager.deleteMachine(
            machine.appName,
            machine.machineId,
          );
        } catch (error) {
          console.log('Machine already deleted');
        }
        await this.removeMachineTracking(userId);
        this.logger.log(`✅ Cleaned up machine for user ${userId}`);
        return {
          success: true,
          message: 'Machine cleaned up successfully',
          userId,
          machineId: machine.machineId,
          appName: machine.appName,
        };
      } else {
        this.logger.log(`No machine found for user ${userId}`);
        return {
          success: true,
          message: 'No machine to cleanup',
          userId,
        };
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup machine for user ${userId}:`, error);
      return {
        success: false,
        message: `Cleanup failed: ${error.message}`,
        userId,
      };
    }
  }

  // =================== SINGLE MACHINE MANAGEMENT METHODS ===================

  /**
   * Get user's current machine (only 1 exists)
   */
  async getCurrentUserMachine(
    userId: number,
  ): Promise<UserMachineRecord | null> {
    try {
      const machine = await this.prisma.userMachine.findUnique({
        where: { userId },
      });
      return machine as UserMachineRecord | null;
    } catch (error) {
      this.logger.error(
        `Failed to get current machine for user ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Create/replace user's machine
   */
  async setUserMachine({
    userId,
    appName,
    machineId,
  }: SetUserMachineParams): Promise<void> {
    try {
      await this.prisma.userMachine.upsert({
        where: { userId },
        create: {
          userId,
          appName,
          machineId,
          status: 'creating',
        },
        update: {
          machineId,
          appName,
          status: 'creating',
        },
      });
      this.logger.log(`✅ Set machine ${machineId} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to set machine for user ${userId}:`, error);
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Update machine status
   */
  async updateMachineStatus({
    userId,
    status,
  }: UpdateMachineStatusParams): Promise<void> {
    try {
      await this.prisma.userMachine.update({
        where: { userId },
        data: { status },
      });
      this.logger.log(
        `✅ Updated machine status to ${status} for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update machine status for user ${userId}:`,
        error,
      );
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Remove machine from tracking
   */
  async removeMachineTracking(userId: number): Promise<void> {
    try {
      await this.prisma.userMachine.delete({
        where: { userId },
      });
      this.logger.log(`✅ Removed machine tracking for user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove machine tracking for user ${userId}:`,
        error,
      );
      // Don't throw error - tracking failure shouldn't break the flow
    }
  }

  /**
   * Create machine for user's existing app - SIMPLIFIED SINGLE MACHINE LOGIC
   */
  async createMachineForUser(userId: number): Promise<MachineResult> {
    this.logger.log(`Creating/reactivating machine for user ${userId}`);

    const user = await this.getUserWithFlyApp(userId);
    const appName = user.fly_app_name;
    const appUrl = `https://${appName}.fly.dev`;

    const currentMachine = await this.getCurrentUserMachine(userId);

    if (!currentMachine) {
      return this.createNewMachineForUser({ userId, appName, appUrl });
    }

    return this.handleExistingMachine({
      userId,
      currentMachine,
      appName,
      appUrl,
    });
  }

  /**
   * Get user with fly app or throw error
   */
  private async getUserWithFlyApp(userId: number): Promise<UserWithFlyApp> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { fly_app_name: true },
    });

    if (!user?.fly_app_name) {
      throw new Error(`User ${userId} has no fly app configured`);
    }

    return user;
  }

  /**
   * Create new machine for user
   * Note: createMachineInExistingApp includes machine infrastructure health check (checkMachineStatus)
   */
  private async createNewMachineForUser({
    userId,
    appName,
    appUrl,
  }: CreateNewMachineForUserParams): Promise<MachineResult> {
    this.logger.log(`Creating new machine for user ${userId}`);

    // This includes machine infrastructure health check internally
    const machine = await this.flyManager.createMachineInExistingApp(appName);
    await this.setUserMachine({ userId, appName, machineId: machine.id });
    await this.updateMachineStatus({ userId, status: 'running' });

    return {
      machineId: machine.id,
      appName,
      appUrl,
      wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
      message: 'New machine created successfully',
    };
  }

  /**
   * Handle existing machine - reactivate or replace
   */
  private async handleExistingMachine({
    userId,
    currentMachine,
    appName,
    appUrl,
  }: HandleExistingMachineParams): Promise<MachineResult> {
    try {
      const machine = await this.flyManager.getMachine(
        appName,
        currentMachine.machineId,
      );

      if (machine.state === 'suspended') {
        return this.reactivateMachine({
          userId,
          currentMachine,
          appName,
          appUrl,
        });
      }

      if (machine.state === 'started') {
        this.logger.log(`Machine ${currentMachine.machineId} already running`);
        return this.buildMachineResponse({
          machineId: currentMachine.machineId,
          appName,
          appUrl,
          message: 'Machine already running',
        });
      }

      // Machine in unknown state - replace it
      return this.replaceBrokenMachine({
        userId,
        currentMachine,
        appName,
        appUrl,
      });
    } catch (error) {
      // Machine doesn't exist on Fly - replace it
      this.logger.warn(
        `Machine ${currentMachine.machineId} not found on Fly, replacing`,
      );
      return this.replaceBrokenMachine({
        userId,
        currentMachine,
        appName,
        appUrl,
      });
    }
  }

  /**
   * Reactivate suspended machine
   */
  private async reactivateMachine({
    userId,
    currentMachine,
    appName,
    appUrl,
  }: ReactivateMachineParams): Promise<MachineResult> {
    this.logger.log(
      `Reactivating suspended machine ${currentMachine.machineId}`,
    );

    // Start the machine and verify infrastructure is ready
    await this.flyManager.startMachine(appName, currentMachine.machineId);
    await this.flyManager.checkMachineStatus(appName, currentMachine.machineId);
    await this.updateMachineStatus({ userId, status: 'running' });

    return this.buildMachineResponse({
      machineId: currentMachine.machineId,
      appName,
      appUrl,
      message: 'Machine reactivated successfully',
    });
  }

  /**
   * Replace broken/missing machine
   * Note: createMachineInExistingApp includes machine infrastructure health check (checkMachineStatus)
   */
  private async replaceBrokenMachine({
    userId,
    currentMachine,
    appName,
    appUrl,
  }: ReplaceBrokenMachineParams): Promise<MachineResult> {
    this.logger.log(`Replacing broken machine ${currentMachine.machineId}`);

    // This includes machine infrastructure health check internally
    const newMachine =
      await this.flyManager.createMachineInExistingApp(appName);
    await this.setUserMachine({ userId, appName, machineId: newMachine.id });
    await this.updateMachineStatus({ userId, status: 'running' });

    return this.buildMachineResponse({
      machineId: newMachine.id,
      appName,
      appUrl,
      message: 'Machine replaced successfully',
    });
  }

  /**
   * Build consistent machine response
   */
  private buildMachineResponse({
    machineId,
    appName,
    appUrl,
    message,
  }: BuildMachineResponseParams): MachineResult {
    return {
      machineId,
      appName,
      appUrl,
      wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
      message,
    };
  }
}
