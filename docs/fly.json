[{"content": "{\"date\":\"2025-07-16T07:59:38.116Z\",\"title\":\"Working with the Machines API\",\"description\":\"Documentation and guides from the team at Fly.io.\",\"url\":\"https://fly.io/docs/machines/api/working-with-machines-api/\",\"sections\":[{\"title\":\"Connecting to the API\",\"subsections\":[{\"title\":\"API addresses\",\"content\":\"There are two base URLs available to connect to the Machines API service.\\n\\n**Internal base URL:** `http://_api.internal:4280`\\n\\n**Public base URL:** `https://api.machines.dev`\\n\\nFrom within your Fly.io [private WireGuard network](https://fly.io/docs/networking/private-networking/), you can connect to the API directly using the internal endpoint. From outside the Fly.io WireGuard mesh, use the public endpoint; this proxies your request to the API.\"},{\"title\":\"Authentication\",\"content\":\"All requests must include an API token in the HTTP headers as follows:\\n\\n``Authorization: Bearer <fly_api_token>``\\n\\nReplace `<fly_api_token>` with a Fly.io authentication token.\"}]},{\"title\":\"Environment setup\",\"content\":\"The examples in the Machines API reference docs assume that you have two environment variables set: `FLY_API_TOKEN`, the authorization token to use with the API call; and `FLY_API_HOSTNAME`, the API base URL.\\n\\nFor local development, you might set them as follows, assuming you have flyctl installed and have authenticated to Fly.io:\\n\\n``$ export FLY_API_HOSTNAME=\\\"https://api.machines.dev\\\" # set to http://_api.internal:4280 when using WireGuard\\n$ export FLY_API_TOKEN=$(fly tokens deploy)``\\n\\nThe `fly tokens deploy` command creates a token capable of managing the application in your current directory. See `fly tokens create --help` for information on other types of tokens you can generate.\"},{\"title\":\"Response Codes\",\"content\":\"The Machines API uses conventional HTTP status codes to provide more information about the response.\\n\\nTypically, 2xx HTTP status codes denote successful operations, 4xx codes imply failures related to the user, and 5xx codes suggest problems with the infrastructure.\\n\\n| Status | Description |\\n| --- | --- |\\n| 200 | Successful request. |\\n| 201 | Created successfully. |\\n| 202 | Accepted (success). |\\n| 204 | Successful request. No content. |\\n| 400 | Bad request. Check that the parameters were correct. |\\n| 401 | The API key used was missing or invalid. |\\n| 404 | The resource was not found. |\\n| 408 | Request timed out. |\\n| 5xx | Indicates an error with Fly.io API servers.\"},{\"title\":\"Rate Limits\",\"content\":\"Machines API rate limits apply _per-action_, _per-machine_ and are scoped per identifier. That might be Machine ID or App ID, depending on the type of request. The limit is 1 request, per second, per action (i.e. Create Machine, Start Machine etc.) — with a short-term burst limit up to 3 req/s, per action.\\n\\nThis applies to all actions except [Get Machine](https://fly.io/docs/machines/api/machines-resource/#get-a-machine) which is 5 req/s, with a short-term burst limit up to 10 req/s.\"}]}"}]