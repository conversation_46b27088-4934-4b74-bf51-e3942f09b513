[{"documentation": [{"title": "Apps", "endpoints": [{"url": "/v1/apps", "method": "POST", "response": {"201": {"id": "string", "created_at": "number"}}, "parameters": {"network": {"type": "string", "required": false, "description": "Name for an IPv6 private network to segment the app onto."}, "app_name": {"type": "string", "required": true, "description": "Name of the app to create"}, "org_slug": {"type": "string", "required": true, "description": "Slug of the org in which to create the app"}, "enable_subdomains": {"type": "boolean", "required": false, "description": "Used for Fly Kubernetes."}}}, {"url": "/v1/apps/{app_name}", "method": "GET", "response": {"200": {"id": "string", "name": "string", "status": "string", "organization": {"name": "string", "slug": "string"}}}, "parameters": {"app_name": {"type": "string", "required": true, "description": "The name of the Fly App to get the details of."}}}, {"url": "/v1/apps/{app_name}", "method": "DELETE", "response": {"202": {}}, "parameters": {"force": {"type": "boolean", "required": false, "description": "Stop all Machines and delete the app immediately."}, "app_name": {"type": "string", "required": true, "description": "The name of the Fly App to delete."}}}, {"url": "/v1/apps", "method": "GET", "response": {"200": {"apps": [{"id": "string", "name": "string", "network": "string", "machine_count": "number"}], "total_apps": "number"}}, "parameters": {"org_slug": {"type": "string", "required": true, "description": "The organization to list apps for."}}}], "description": "You can use the Apps resource to create and manage Fly Apps. A Fly App is an abstraction for a group of Machines running your code, along with the configuration, provisioned resources, and data we need to keep track of to run and route to your Machines."}]}]