# Fly.io Preview Implementation Guide

## Overview

This guide provides a complete implementation for switching from Koyeb to Fly.io for the live preview feature. The implementation uses Fly.io's Machines API to create fast-launching VMs with the same architecture as the current Koyeb system.

## Architecture Comparison

### Koyeb vs Fly.io Mapping

- **Koyeb App** → **Fly.io App** (top-level container with domain)
- **Koyeb Web Service** → **Fly.io Machine** (runs the Docker container)
- **Koyeb Deployment** → **Fly.io Machine State** (running instance)

### Core Concept

1. **Pre-built Docker image** with Next.js + all dependencies pre-installed
2. **HTTP API endpoint** in container for receiving AI-generated code
3. **Instant deployment** since no build/upload time needed
4. **Code injection** after machine is running
5. **Sub-5-second total time** from request to preview

## Implementation Steps

### Phase 1: Environment Setup

1. **Environment Variables**

```bash
# Add to .env
FLY_API_TOKEN=your_fly_api_token
FLY_ORG_SLUG=your_organization_slug
PREVIEW_TIMEOUT=120000  # 2 minutes
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest
```

### Phase 2: Create Fly.io Types

Create `apps/core/src/preview/fly-types.ts`:

```typescript
// **************** Fly.io App Types ****************
export interface FlyApp {
  id: string;
  name: string;
  status: string;
  organization: {
    name: string;
    slug: string;
  };
}

export interface FlyCreateAppResponse {
  id: string;
  created_at: number;
}

// **************** Fly.io Machine Types ****************
export interface FlyMachine {
  id: string;
  name: string;
  state: string;
  config: FlyMachineConfig;
  events: FlyMachineEvent[];
  region: string;
  image_ref: {
    tag: string;
    digest: string;
    registry: string;
    repository: string;
  };
  created_at: string;
  private_ip: string;
  updated_at: string;
  instance_id: string;
}

export interface FlyMachineConfig {
  init?: {
    exec: string[];
  };
  guest: {
    cpus: number;
    cpu_kind: string;
    memory_mb: number;
  };
  image: string;
  restart: {
    policy: string;
  };
  auto_destroy: boolean;
  services?: Array<{
    ports: Array<{
      port: number;
      handlers: string[];
    }>;
    protocol: string;
    internal_port: number;
  }>;
  env?: Record<string, string>;
}

export interface FlyMachineEvent {
  type: string;
  source: string;
  status: string;
  timestamp: number;
}

export interface FlyCreateMachineRequest {
  name: string;
  config: FlyMachineConfig;
  region: string;
  lease_ttl?: number;
  skip_launch?: boolean;
  skip_service_registration?: boolean;
}

export interface FlyWaitResponse {
  ok: boolean;
}

// **************** Machine States ****************
export type FlyMachineState =
  | 'created'
  | 'starting'
  | 'started'
  | 'stopping'
  | 'stopped'
  | 'replacing'
  | 'destroying'
  | 'destroyed';
```

### Phase 3: Create Fly.io Preview Manager

Create `apps/core/src/preview/fly-preview-manager.ts`:

```typescript
import {
  FlyApp,
  FlyMachine,
  FlyCreateAppResponse,
  FlyCreateMachineRequest,
  FlyWaitResponse,
  FlyMachineState,
} from './fly-types';
import {
  TWO_MINUTES_MS,
  THIRTY_SEC_MS,
  HEALTH_CHECK_INTERVAL_MS,
  APP_PORT,
  INJECT_CODE_PATH,
} from './constants';

export class FlyPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://api.machines.dev';
  private dockerImage: string;
  private orgSlug: string;

  constructor(apiKey: string, dockerImage: string, orgSlug: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
    this.orgSlug = orgSlug;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseText = await response.text();

      if (!response.ok) {
        console.error(`Fly.io API error: ${response.status} - ${responseText}`);
        throw new Error(
          `Fly.io API error: ${response.status} - ${responseText}`,
        );
      }

      try {
        return JSON.parse(responseText);
      } catch (error) {
        console.error('Failed to parse response as JSON:', responseText);
        throw new Error(`Failed to parse Fly.io API response: ${error.message}`);
      }
    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }

  async createApp(appName: string): Promise<FlyCreateAppResponse> {
    return this.makeRequest<FlyCreateAppResponse>('/v1/apps', 'POST', {
      app_name: appName,
      org_slug: this.orgSlug,
    });
  }

  async createPreviewApp(): Promise<{
    machineId: string;
    appId: string;
    appUrl: string;
  }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = await this.createApp(appName);

    // Step 2: Create Machine inside the App
    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: {
          cpus: 1,
          cpu_kind: 'shared',
          memory_mb: 512,
        },
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [
          {
            ports: [
              {
                port: 80,
                handlers: ['http'],
              },
              {
                port: 443,
                handlers: ['tls', 'http'],
              },
            ],
            protocol: 'tcp',
            internal_port: APP_PORT,
          },
        ],
        env: {
          NODE_ENV: 'development',
        },
      },
      region: 'sin', // Singapore region
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      const machine = await this.makeRequest<FlyMachine>(
        `/v1/apps/${appName}/machines`,
        'POST',
        machineConfig,
      );

      // Step 3: Wait for the machine to be ready
      await this.waitForMachineReady(appName, machine.id);

      // Step 4: Get the App URL
      const appUrl = `https://${appName}.fly.dev`;

      // Schedule auto-destruction after 2 minutes
      setTimeout(
        () => {
          this.destroyApp(appName);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || TWO_MINUTES_MS.toString()),
      );

      return {
        machineId: machine.id,
        appId: app.id,
        appUrl,
      };
    } catch (error) {
      console.error('Machine creation failed:', error);
      console.error(
        'Machine config:',
        JSON.stringify(machineConfig, null, 2),
      );

      // Clean up the app if machine creation fails
      try {
        await this.makeRequest(`/v1/apps/${appName}`, 'DELETE', { force: true });
        console.log(`App ${appName} deleted after machine creation failure`);
      } catch (cleanupError) {
        console.error(`Failed to clean up app ${appName}:`, cleanupError);
      }

      throw error;
    }
  }
```

async getMachine(appName: string, machineId: string): Promise<FlyMachine> {
return this.makeRequest<FlyMachine>(
`/v1/apps/${appName}/machines/${machineId}`,
);
}

async waitForMachineReady(appName: string, machineId: string): Promise<void> {
let attempts = 0;
const maxAttempts = 24; // 4 minutes max wait (24 \* 10s)

    while (attempts < maxAttempts) {
      try {
        // Wait for machine to be in 'started' state
        await this.makeRequest<FlyWaitResponse>(
          `/v1/apps/${appName}/machines/${machineId}/wait?state=started&timeout=30`,
        );

        // Additional health check - try to reach the app
        const appUrl = `https://${appName}.fly.dev`;
        await this.performHealthCheck(appUrl);

        console.log(`Machine ${machineId} is ready and healthy`);
        return;
      } catch (error) {
        console.log(`Machine ${machineId} not ready yet, attempt ${attempts + 1}`);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
      }
    }

    throw new Error('Machine failed to become ready within timeout');

}

private async performHealthCheck(appUrl: string): Promise<void> {
const healthUrl = `${appUrl}/api/health`;
let attempts = 0;
const maxAttempts = 60; // 30 seconds (60 \* 500ms)

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(healthUrl, {
          method: 'GET',
          timeout: 5000,
        });

        if (response.ok) {
          console.log('Health check passed');
          return;
        }
      } catch (error) {
        // Continue trying
      }

      await new Promise((resolve) => setTimeout(resolve, HEALTH_CHECK_INTERVAL_MS));
      attempts++;
    }

    throw new Error('Health check failed within timeout');

}

async injectCode(
appUrl: string,
files: Record<string, string>,
): Promise<void> {
try {
const injectionUrl = `${appUrl}${INJECT_CODE_PATH}`;

      console.log(`Attempting code injection to: ${injectionUrl}`);

      const response = await fetch(injectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files }),
      });

      if (!response.ok) {
        throw new Error(`Code injection failed: still starting`);
      }

      const result = await response.json();
      console.log('Code injection successful:', result.message);
    } catch (e) {
      console.error('Code injection failed:', e);
    }

}

async destroyApp(appName: string): Promise<void> {
try {
// Delete the app - this will automatically delete all associated machines
await this.makeRequest(`/v1/apps/${appName}`, 'DELETE', { force: true });
console.log(`App ${appName} deleted successfully (machines auto-deleted)`);
} catch (error) {
console.error(`Failed to destroy app ${appName}:`, error);
}
}
}

````

### Phase 4: Update Constants

Add Fly.io specific constants to `apps/core/src/preview/constants.ts`:

```typescript
// ==================== FLY.IO SERVICE CONFIGURATION ====================

/** Fly.io machine CPU count */
export const FLY_MACHINE_CPUS = 1;

/** Fly.io machine CPU kind */
export const FLY_MACHINE_CPU_KIND = 'shared';

/** Fly.io machine memory in MB */
export const FLY_MACHINE_MEMORY_MB = 512;

/** Fly.io region */
export const FLY_REGION = 'sin';

/** Fly.io machine restart policy */
export const FLY_RESTART_POLICY = 'no';

/** Fly.io machine auto destroy */
export const FLY_AUTO_DESTROY = true;

// ==================== FLY.IO MACHINE STATES ====================

/** Machine state indicating created */
export const CREATED_STATE = 'created';

/** Machine state indicating starting */
export const STARTING_STATE = 'starting';

/** Machine state indicating started */
export const STARTED_STATE = 'started';

/** Machine state indicating stopped */
export const STOPPED_STATE = 'stopped';

/** Machine state indicating destroyed */
export const DESTROYED_STATE = 'destroyed';
````

### Phase 5: Create Fly.io Preview Service

Create `apps/core/src/preview/fly-preview.service.ts`:

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { FlyPreviewManager } from './fly-preview-manager';
import {
  THIRTY_SEC_MS,
  HEALTH_CHECK_INTERVAL_MS,
  HEALTHY_STATUS,
  UNHEALTHY_STATUS,
  DELETED_STATUS,
  MIN_MESSAGES_LENGTH,
} from './constants';

@Injectable()
export class FlyPreviewService {
  private readonly logger = new Logger(FlyPreviewService.name);
  private flyManager: FlyPreviewManager;

  constructor() {
    const apiKey = process.env.FLY_API_TOKEN;
    const dockerImage = process.env.DOCKER_IMAGE;
    const orgSlug = process.env.FLY_ORG_SLUG;

    if (!apiKey) {
      throw new Error('FLY_API_TOKEN environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    if (!orgSlug) {
      throw new Error('FLY_ORG_SLUG environment variable is required');
    }

    this.flyManager = new FlyPreviewManager(apiKey, dockerImage, orgSlug);
    this.logger.log('FlyPreviewService initialized with Fly.io integration');
  }

  async createPreview(files: Record<string, string>) {
    this.logger.log('Starting preview creation process');

    try {
      // Step 1: Create Fly.io app with machine (2-3 seconds)
      this.logger.log('Creating Fly.io app with machine...');

      const createResult = await this.flyManager.createPreviewApp();

      const { machineId, appId, appUrl } = createResult;

      // Step 2: Files are already in the correct format
      this.logger.log(
        `Processing ${Object.keys(files).length} files for injection`,
      );

      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Check for required files
      const hasPackageJson = Object.keys(files).some((path) =>
        path.includes('package.json'),
      );
      const hasNextConfig = Object.keys(files).some((path) =>
        path.includes('next.config'),
      );

      this.logger.log(
        `Files validation - package.json: ${hasPackageJson}, next.config: ${hasNextConfig}`,
      );

      // Step 3: Inject code into running machine (1 second)
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      const result = {
        machineId,
        appId,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  async healthCheck() {
    return {
      status: 'ok',
      service: 'fly-preview',
      timestamp: new Date().toISOString(),
    };
  }
}
```

### Phase 6: Update Preview Service to Support Both Providers

Modify `apps/core/src/preview/preview.service.ts` to support both Koyeb and Fly.io:

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { KoyebPreviewManager } from './koyeb-preview-manager';
import { FlyPreviewManager } from './fly-preview-manager';
import {
  THIRTY_SEC_MS,
  HEALTH_CHECK_INTERVAL_MS,
  HEALTHY_STATUS,
  UNHEALTHY_STATUS,
  DELETED_STATUS,
  MIN_MESSAGES_LENGTH,
} from './constants';

type PreviewProvider = 'koyeb' | 'fly';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private koyebManager?: KoyebPreviewManager;
  private flyManager?: FlyPreviewManager;
  private provider: PreviewProvider;

  constructor() {
    // Determine which provider to use based on environment variables
    this.provider =
      (process.env.PREVIEW_PROVIDER as PreviewProvider) || 'koyeb';

    if (this.provider === 'fly') {
      const apiKey = process.env.FLY_API_TOKEN;
      const dockerImage = process.env.DOCKER_IMAGE;
      const orgSlug = process.env.FLY_ORG_SLUG;

      if (!apiKey) {
        throw new Error('FLY_API_TOKEN environment variable is required');
      }

      if (!dockerImage) {
        throw new Error('DOCKER_IMAGE environment variable is required');
      }

      if (!orgSlug) {
        throw new Error('FLY_ORG_SLUG environment variable is required');
      }

      this.flyManager = new FlyPreviewManager(apiKey, dockerImage, orgSlug);
      this.logger.log('PreviewService initialized with Fly.io integration');
    } else {
      const apiKey = process.env.KOYEB_API_KEY;
      const dockerImage = process.env.DOCKER_IMAGE;

      if (!apiKey) {
        throw new Error('KOYEB_API_KEY environment variable is required');
      }

      if (!dockerImage) {
        throw new Error('DOCKER_IMAGE environment variable is required');
      }

      this.koyebManager = new KoyebPreviewManager(apiKey, dockerImage);
      this.logger.log('PreviewService initialized with Koyeb integration');
    }
  }

  async createPreview(files: Record<string, string>) {
    this.logger.log(`Starting preview creation process with ${this.provider}`);

    try {
      if (this.provider === 'fly' && this.flyManager) {
        return await this.createFlyPreview(files);
      } else if (this.provider === 'koyeb' && this.koyebManager) {
        return await this.createKoyebPreview(files);
      } else {
        throw new Error(`Invalid provider configuration: ${this.provider}`);
      }
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  private async createFlyPreview(files: Record<string, string>) {
    this.logger.log('Creating Fly.io app with machine...');

    const createResult = await this.flyManager!.createPreviewApp();
    const { machineId, appId, appUrl } = createResult;

    this.logger.log(
      `Processing ${Object.keys(files).length} files for injection`,
    );

    // Validate files
    if (!files || Object.keys(files).length === 0) {
      throw new Error('No files provided for preview');
    }

    // Step 3: Inject code into running machine
    this.logger.log('Injecting code into machine...');
    await this.flyManager!.injectCode(appUrl, files);
    this.logger.log('Code injection completed successfully');

    const result = {
      serviceId: machineId, // For compatibility with existing frontend
      appId,
      previewUrl: appUrl,
      wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
      filesCount: Object.keys(files).length,
      provider: 'fly',
    };

    this.logger.log(
      `Fly.io preview created successfully: ${result.previewUrl}`,
    );
    return result;
  }

  private async createKoyebPreview(files: Record<string, string>) {
    this.logger.log('Creating Koyeb app with web service...');

    const createResult = await this.koyebManager!.createPreviewApp();
    const { serviceId, appId, appUrl } = createResult;

    this.logger.log(
      `Processing ${Object.keys(files).length} files for injection`,
    );

    // Validate files
    if (!files || Object.keys(files).length === 0) {
      throw new Error('No files provided for preview');
    }

    // Step 3: Inject code into running container
    this.logger.log('Injecting code into container...');
    await this.koyebManager!.injectCode(appUrl, files);
    this.logger.log('Code injection completed successfully');

    const result = {
      serviceId,
      appId,
      previewUrl: appUrl,
      wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
      filesCount: Object.keys(files).length,
      provider: 'koyeb',
    };

    this.logger.log(`Koyeb preview created successfully: ${result.previewUrl}`);
    return result;
  }

  async healthCheck() {
    return {
      status: 'ok',
      service: 'preview',
      provider: this.provider,
      timestamp: new Date().toISOString(),
    };
  }
}
```

### Phase 7: Environment Configuration

Update your `.env` file to support both providers:

```bash
# Preview Provider Configuration
PREVIEW_PROVIDER=fly  # or 'koyeb'

# Fly.io Configuration (when PREVIEW_PROVIDER=fly)
FLY_API_TOKEN=your_fly_api_token
FLY_ORG_SLUG=your_organization_slug

# Koyeb Configuration (when PREVIEW_PROVIDER=koyeb)
KOYEB_API_KEY=your_koyeb_api_key

# Common Configuration
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest
PREVIEW_TIMEOUT=120000  # 2 minutes
```

### Phase 8: Testing and Validation

1. **Test Fly.io API Connection**

```bash
# Test your Fly.io API token
curl -H "Authorization: Bearer $FLY_API_TOKEN" https://api.machines.dev/v1/apps
```

2. **Test App Creation**

```bash
# Create a test app
curl -X POST \
  -H "Authorization: Bearer $FLY_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"app_name": "test-app", "org_slug": "your-org-slug"}' \
  https://api.machines.dev/v1/apps
```

3. **Validate Docker Image**
   Ensure your Docker image is accessible and contains the required API endpoints:

- `/api/health` - Health check endpoint
- `/api/inject-code` - Code injection endpoint

### Phase 9: Migration Strategy

1. **Gradual Migration**

   - Set `PREVIEW_PROVIDER=koyeb` initially
   - Test Fly.io implementation in development
   - Switch to `PREVIEW_PROVIDER=fly` when ready

2. **Rollback Plan**

   - Keep Koyeb configuration in environment
   - Switch back to `PREVIEW_PROVIDER=koyeb` if issues occur

3. **Monitoring**
   - Monitor preview creation success rates
   - Track response times
   - Monitor resource usage

### Phase 10: Key Differences from Koyeb

| Aspect             | Koyeb                        | Fly.io                         |
| ------------------ | ---------------------------- | ------------------------------ |
| **API Structure**  | App → Service → Deployment   | App → Machine                  |
| **Health Checks**  | Built-in deployment status   | Manual health check + wait API |
| **Scaling**        | Service-level scaling config | Machine-level auto_destroy     |
| **Regions**        | `fra`, `sin`                 | `sin`, `lax`, `fra`            |
| **Instance Types** | `nano`, `small`              | CPU/Memory specification       |
| **Auto-cleanup**   | App deletion                 | App deletion with force flag   |

### Phase 11: Error Handling

The Fly.io implementation includes comprehensive error handling:

1. **Machine Creation Failures**

   - Automatic app cleanup on machine creation failure
   - Detailed error logging with machine configuration

2. **Health Check Failures**

   - Timeout-based health checks with retry logic
   - Graceful degradation when health checks fail

3. **Code Injection Failures**
   - Retry logic for code injection
   - Fallback error messages

### Phase 12: Performance Optimizations

1. **Machine Configuration**

   - Optimized CPU/memory allocation (1 CPU, 512MB)
   - Auto-destroy enabled for automatic cleanup
   - Skip service registration for faster startup

2. **Health Check Strategy**

   - Combined machine state waiting + HTTP health checks
   - Configurable timeout values
   - Efficient polling intervals

3. **Resource Management**
   - Automatic cleanup after 2 minutes
   - Force deletion for immediate resource reclamation
   - Proper error handling to prevent resource leaks

## Implementation Checklist

- [ ] Create Fly.io types (`fly-types.ts`)
- [ ] Implement Fly.io preview manager (`fly-preview-manager.ts`)
- [ ] Update constants with Fly.io configurations
- [ ] Modify preview service to support both providers
- [ ] Update environment variables
- [ ] Test API connectivity
- [ ] Validate Docker image compatibility
- [ ] Deploy and test in development
- [ ] Monitor performance and error rates
- [ ] Switch to production when stable

## Conclusion

This implementation provides a seamless migration path from Koyeb to Fly.io while maintaining the same fast preview generation capabilities. The dual-provider approach allows for gradual migration and easy rollback if needed.

The Fly.io Machines API provides similar functionality to Koyeb's service model, with the main difference being the direct machine management approach versus the service abstraction layer in Koyeb.
