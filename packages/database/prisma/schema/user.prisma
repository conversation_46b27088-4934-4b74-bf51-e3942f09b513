model User {
  id                               Int                         @id @default(autoincrement())
  email                            String                      @unique
  password_hash                    String?
  last_login                       DateTime?
  created_at                       DateTime                    @default(now())
  updated_at                       DateTime                    @updatedAt
  deleted_at                       DateTime?
  is_active                        <PERSON><PERSON><PERSON>                     @default(false)
  is_email_verified                <PERSON><PERSON><PERSON>                     @default(false)
  email_verified_at                DateTime?
  verification_secret              String?
  is_two_factor_enabled            Boolean                     @default(false)
  two_factor_secret                String?
  fly_app_name                     String? // Store the fly app name for persistent apps
  refresh_tokens                   RefreshToken[]
  recovery_codes                   RecoveryCode[]
  oauth_providers                  OauthProvider[]
  profile                          UserProfile[]
  workspaces                       Workspace[]
  workspace_members                WorkspaceMember[]
  workspace_members_invitations_by WorkspaceMemberInvitation[] @relation("workspace_members_invitations_by")
  chat_history                     PageChatHistory[]
  user_machine                     UserMachine? // Single machine relationship

  @@map("users")
}
