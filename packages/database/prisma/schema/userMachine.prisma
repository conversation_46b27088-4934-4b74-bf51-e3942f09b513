model UserMachine {
  id        String   @id @default(cuid())
  userId    Int      @unique // Unique constraint enforces 1 machine per user
  appName   String
  machineId String   @unique
  status    String // 'creating' | 'running' | 'suspended' | 'destroyed'
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("user_machines")
}
